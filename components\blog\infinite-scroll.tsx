"use client"

import { useState, useEffect, useCallback } from 'react'
import { BlogPost } from '@/types/blog'
import { BlogCard } from './blog-card'
import { Loader2 } from 'lucide-react'

interface InfiniteScrollProps {
  initialPosts: BlogPost[]
  searchQuery?: string
  category?: string
  author?: string
  className?: string
}

interface PaginationResponse {
  posts: BlogPost[]
  pagination: {
    currentPage: number
    totalPages: number
    totalPosts: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export function InfiniteScroll({
  initialPosts,
  searchQuery = '',
  category = '',
  author = '',
  className = '',
}: InfiniteScrollProps) {
  const [posts, setPosts] = useState<BlogPost[]>(initialPosts)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasNextPage, setHasNextPage] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Reset when search/filter changes
  useEffect(() => {
    setPosts(initialPosts)
    setCurrentPage(1)
    setHasNextPage(true)
    setError(null)
  }, [initialPosts, searchQuery, category, author])

  const loadMorePosts = useCallback(async () => {
    if (isLoading || !hasNextPage) return

    setIsLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        page: (currentPage + 1).toString(),
        limit: '12',
      })

      if (searchQuery) params.set('q', searchQuery)
      if (category) params.set('category', category)
      if (author) params.set('author', author)

      const response = await fetch(`/api/blog?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to load more posts')
      }

      const data: PaginationResponse = await response.json()
      
      setPosts(prev => [...prev, ...data.posts])
      setCurrentPage(data.pagination.currentPage)
      setHasNextPage(data.pagination.hasNextPage)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load more posts')
    } finally {
      setIsLoading(false)
    }
  }, [currentPage, hasNextPage, isLoading, searchQuery, category, author])

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0]
        if (target.isIntersecting && hasNextPage && !isLoading) {
          loadMorePosts()
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px',
      }
    )

    const sentinel = document.getElementById('scroll-sentinel')
    if (sentinel) {
      observer.observe(sentinel)
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel)
      }
    }
  }, [loadMorePosts, hasNextPage, isLoading])

  return (
    <div className={className}>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.map((post) => (
          <BlogCard key={post.id} post={post} />
        ))}
      </div>

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2 text-sm text-muted-foreground">Loading more posts...</span>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="text-center py-8">
          <p className="text-red-600 text-sm">{error}</p>
          <button
            onClick={loadMorePosts}
            className="mt-2 text-sm text-blue-600 hover:text-blue-700 underline"
          >
            Try again
          </button>
        </div>
      )}

      {/* End of posts message */}
      {!hasNextPage && posts.length > 0 && (
        <div className="text-center py-8">
          <p className="text-sm text-muted-foreground">You&apos;ve reached the end of the posts!</p>
        </div>
      )}

      {/* No posts message */}
      {posts.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            {searchQuery ? `No posts found for "${searchQuery}"` : 'No posts found.'}
          </p>
        </div>
      )}

      {/* Intersection observer sentinel */}
      <div id="scroll-sentinel" className="h-1" />
    </div>
  )
}
