"use client"

import Image from "next/image"
import { ArrowRight } from "lucide-react"

interface Feature {
  title: string
  description: string
  image: string
  benefits: string[]
}

interface FeaturesSectionProps {
  className?: string
  feature: Feature
}

export default function FeaturesSection({
  className,
  feature
}: FeaturesSectionProps) {
  return (
    <section className={`w-full py-2 md:py-24 bg-gray-50 ${className}`}>
      <div className="container px-4 md:px-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-12 md:gap-16">
          <div className="mx-auto md:mx-0 max-w-[600px] space-y-4 text-center md:text-left md:flex-1">
            <h2 className="text-3xl font-semibold tracking-tight sm:text-4xl">
              {feature.title}
            </h2>
            <p className="text-base text-muted-foreground">
              {feature.description}
            </p>
            <ul className="space-y-2 text-left">
              {feature.benefits.map((benefit, index) => (
                <li key={index} className="flex items-center gap-2">
                  <ArrowRight className="h-4 w-4 text-primary shrink-0" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
          <div className="md:flex-1">
            <div className="rounded-lg overflow-hidden">
              <Image
                src={feature.image}
                alt={feature.title}
                width={600}
                height={400}
                className="w-full h-auto object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
