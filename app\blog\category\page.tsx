import Link from "next/link"
import { Container } from "@/components/ui/container"
import { Metadata } from "next"
import Image from "next/image"
import { getCategoriesWithPostCounts } from "@/lib/categories"
import { cn } from "@/lib/utils"

export const metadata: Metadata = {
  title: "Categories | Swadesic Blog",
  description: "Browse all blog post categories on Swadesic. Find articles about ecommerce, small business, and more.",
  openGraph: {
    title: "Categories | Swadesic Blog",
    description: "Browse all blog post categories on Swadesic. Find articles about ecommerce, small business, and more.",
  },
}

export default async function CategoriesPage() {
  const categories = await getCategoriesWithPostCounts()

  // If no categories found, show a message
  if (categories.length === 0) {
    return (
      <Container as="main" className="py-12 text-center">
        <h1 className="text-3xl font-bold mb-4">No Categories Found</h1>
        <p className="text-muted-foreground mb-6">
          We couldn&apos;t find any blog categories at the moment. Please check back later.
        </p>
        <Link
          href="/blog"
          className="inline-flex items-center text-primary hover:underline"
        >
          ← Back to all posts
        </Link>
      </Container>
    )
  }

  return (
    <Container as="main" className="py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Browse by Category</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Explore our blog posts organized by topic
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {categories.map((category) => (
          <Link
            key={category.slug}
            href={`/blog/category/${category.slug}`}
            className={cn(
              "group flex flex-col h-full rounded-xl overflow-hidden border border-border",
              "hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
            )}
            style={category.color ? { '--category-color': category.color } as React.CSSProperties : undefined}
          >
            {/* Category Image */}
            <div className="relative h-48 w-full overflow-hidden">
              <Image
                src={category.image}
                alt={`${category.name} category`}
                fill
                className="object-cover transition-transform duration-500 group-hover:scale-105"
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                priority={false}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div className="absolute bottom-0 left-0 right-0 p-4">
                <h2 className="text-xl font-bold text-white group-hover:text-primary transition-colors">
                  {category.name}
                </h2>
                <span 
                  className={cn(
                    "inline-flex items-center px-3 py-1 mt-2 rounded-full text-xs font-medium",
                    "bg-primary/90 text-white"
                  )}
                >
                  {category.count} {category.count === 1 ? 'Post' : 'Posts'}
                </span>
              </div>
            </div>

            {/* Category Content */}
            <div className="p-6 flex-1 flex flex-col bg-card">
              <p className="text-muted-foreground mb-4 flex-1">
                {category.description}
              </p>
              <div className="text-primary font-medium group-hover:underline inline-flex items-center">
                View all {category.name} posts
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="16" 
                  height="16" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  className="ml-1 group-hover:translate-x-1 transition-transform"
                >
                  <path d="M5 12h14"/>
                  <path d="m12 5 7 7-7 7"/>
                </svg>
              </div>
            </div>
          </Link>
        ))}
      </div>

      <div className="mt-12 pt-8 border-t">
        <Link
          href="/blog"
          className="inline-flex items-center text-primary hover:underline"
        >
          ← Back to all articles
        </Link>
      </div>
    </Container>
  )
}
