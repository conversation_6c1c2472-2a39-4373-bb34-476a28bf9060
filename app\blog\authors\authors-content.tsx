import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { getAuthorData, getAllBlogPosts } from "@/lib/mdx"
import type { BlogPost } from "@/types/blog"
import { ExternalLink, User } from "lucide-react"
import { Container } from "@/components/ui/container"

export const metadata: Metadata = {
  title: "Authors | Swadesic Blog",
  description: "Meet the writers and contributors behind the Swadesic blog. Learn about their expertise and read their latest articles.",
  openGraph: {
    title: "Authors | Swadesic Blog",
    description: "Meet the writers and contributors behind the Swadesic blog.",
  },
}

interface AuthorWithStats {
  slug: string
  name: string
  image: string
  bio?: string
  social?: {
    twitter?: string
    linkedin?: string
    github?: string
  }
  postCount: number
}

export default async function AuthorsContent() {
  // Get all blog posts to count posts per author
  const allPosts = await getAllBlogPosts()
  
  // Get author data
  const authorsData = getAuthorData()
  
  // Count posts per author
  const authorPostCounts = allPosts.reduce<Record<string, number>>((acc, post) => {
    const authorSlug = post.author.slug
    acc[authorSlug] = (acc[authorSlug] || 0) + 1
    return acc
  }, {})

  // Combine author data with post counts
  const authors: AuthorWithStats[] = authorsData.map(author => ({
    ...author,
    postCount: authorPostCounts[author.slug] || 0
  }))

  // Sort authors by number of posts (descending)
  const sortedAuthors = [...authors].sort((a, b) => b.postCount - a.postCount)

  return (
    <Container as="main" className="py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Our Authors</h1>
          <p className="text-muted-foreground">
            Meet the talented writers and contributors behind the Swadesic blog
          </p>
        </div>

        {/* Authors Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedAuthors.map((author) => (
            <div key={author.slug} className="bg-card p-6 rounded-lg border border-border">
              <div className="flex items-center space-x-4">
                <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-full border">
                  <Image
                    src={author.image}
                    alt={author.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h2 className="text-lg font-semibold">
                    <Link href={`/blog/author/${author.slug}`} className="hover:underline">
                      {author.name}
                    </Link>
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    {author.postCount} {author.postCount === 1 ? 'post' : 'posts'}
                  </p>
                </div>
              </div>
              
              {author.bio && (
                <p className="mt-4 text-sm text-muted-foreground">
                  {author.bio}
                </p>
              )}

              {(author.social?.twitter || author.social?.linkedin || author.social?.github) && (
                <div className="mt-4 flex space-x-4">
                  {author.social?.twitter && (
                    <a
                      href={`https://twitter.com/${author.social.twitter}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground transition-colors"
                      aria-label={`${author.name}'s Twitter`}
                    >
                      <span className="sr-only">Twitter</span>
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  )}
                  {author.social?.linkedin && (
                    <a
                      href={`https://linkedin.com/in/${author.social.linkedin}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground transition-colors"
                      aria-label={`${author.name}'s LinkedIn`}
                    >
                      <span className="sr-only">LinkedIn</span>
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  )}
                  {author.social?.github && (
                    <a
                      href={`https://github.com/${author.social.github}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-foreground transition-colors"
                      aria-label={`${author.name}'s GitHub`}
                    >
                      <span className="sr-only">GitHub</span>
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  )}
                </div>
              )}
              
              <div className="mt-4">
                <Link
                  href={`/blog/author/${author.slug}`}
                  className="inline-flex items-center text-sm font-medium text-primary hover:underline"
                >
                  View all posts by {author.name.split(' ')[0]}
                  <svg
                    className="ml-1 h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Container>
  )
}
