"use client"

import Link from "next/link"
import { Blog<PERSON>ate<PERSON><PERSON>, BlogTag } from "@/types/blog"
import { cn } from "@/lib/utils"
import { Hash, Folder } from "lucide-react"

interface BlogSidebarProps {
  categories: BlogCategory[]
  tags: BlogTag[]
}

export function BlogSidebar({ categories, tags }: BlogSidebarProps) {
  return (
    <aside className="space-y-8">
      <section className="space-y-4">
        <div className="flex items-center gap-2">
          <Folder className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Categories</h3>
        </div>
        <div className="space-y-1">
          {categories.map((category) => (
            <Link
              key={category.slug}
              href={`/blog/category/${category.slug}`}
              className={cn(
                "block py-2 px-3 rounded-md text-sm transition-colors",
                "hover:bg-accent hover:text-accent-foreground",
                "text-muted-foreground hover:text-foreground"
              )}
            >
              {category.name}
              <span className="float-right text-xs bg-secondary px-2 py-0.5 rounded-full">
                {category.count}
              </span>
            </Link>
          ))}
        </div>
      </section>

      <section className="space-y-4">
        <div className="flex items-center gap-2">
          <Hash className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Popular Tags</h3>
        </div>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <Link
              key={tag.slug}
              href={`/blog/tag/${tag.slug}`}
              className={cn(
                "text-xs px-3 py-1.5 rounded-full transition-colors",
                "bg-secondary/50 hover:bg-secondary",
                "text-secondary-foreground"
              )}
            >
              {tag.name}
            </Link>
          ))}
        </div>
      </section>
    </aside>
  )
}
