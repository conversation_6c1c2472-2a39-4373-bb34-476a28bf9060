import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, ArrowUpRight,  Globe,LineChart, ShoppingBag,  ArrowDown } from 'lucide-react'
import FeaturesSectionAdaptive from "@/components/features-section-adaptive"
import FeaturesSectionResponsiveImage from "@/components/features-section-responsive-image"
import FAQSection from "@/components/faq-section"
import { parseFAQs } from "@/lib/parse-faqs"
import fs from 'fs'
import path from 'path'
import { StoryCarousel } from "@/components/story-carousel"



const buyerFeatures = {
  title: "Discover the Swadesic Experience",
  description: "Shop with confidence and discover authentic Swadeshi products with our unique features",
  features: [
    {
      title: "All Stores at One Place",
      description: "Find all your favorite Swadeshi stores and products in one convenient location. No more jumping between different platforms.",
      image: "/Swadesic-web-prep/buyer/All stores at one place.jpg"
    },
    {
      title: "Know the Real Identity",
      description: "Shop with confidence knowing every seller's identity is verified. We ensure transparency and trust in every transaction.",
      image: "/Swadesic-web-prep/buyer/Know the real identity.jpg"
    },
    {
      title: "Orders Tracking & Notification",
      description: "Stay updated with real-time order tracking and instant notifications. Never miss an update about your purchase.",
      image: "/Swadesic-web-prep/buyer/Orders tracking & Notification.jpg"
    },
    {
      title: "Products that Speak",
      description: "Detailed product information and high-quality images help you make informed decisions. Every product tells its own story.",
      image: "/Swadesic-web-prep/buyer/Products that speak.jpg"
    },
    {
      title: "In-app Messaging",
      description: "Connect directly with stores for any questions or concerns before you make an order, ensuring your privacy and ease of communication.",
      image: "/Swadesic-web-prep/app/In-app messaging.jpg"
    }
  ]
}

const storyPhases = [
  {
    iconName: "Factory",
    number: 1,
    title: "Money Stays In Bharat   ",
    description: "Your ₹1,500 stays in Bharat, helping the local shoemaker pay wages, buy materials, and invest in growth. Over time, as demand increases, the shoemaker improves production and quality."
  },
  {
    iconName: "LineChart",
    number: 2,
    title: "Improving & Scaling",
    description: "As business grows, the shoemaker becomes more efficient, reducing costs, and improving quality. They can now lower the price of shoes."
  },
  {
    iconName: "IndianRupee",
    number: 3,
    title: "Price Reduction",
    description: "With improved efficiency, the Swadeshi shoes’ price drops to ₹1,200 or even ₹1,000, while maintaining quality."
  }
];

export default async function Home() {
  // Read and parse buyer FAQs
  const buyerFAQsPath = path.join(process.cwd(), 'public/Markdowns/buyer/buyer-faqs.md')
  const buyerFAQsContent = fs.readFileSync(buyerFAQsPath, 'utf8')
  const buyerFAQs = parseFAQs(buyerFAQsContent)

  return (
    <div className="flex flex-col min-h-screen overflow-x-hidden">
      <main className="flex-1 overflow-x-hidden">
        {/* Hero Section */}
        <div className="flex min-h-[75vh] items-start sm:items-center justify-center bg-white px-4 pt-32 sm:p-4 md:py-8">
          <div className="mx-auto max-w-[700px] space-y-4 text-center md:pt-16">
            <p className="text-sm text-muted-foreground">
              Swadesic
            </p>
            <h1 className="font-heading text-4xl leading-[1.1] md:text-6xl">
              Discover & Shop, Swadeshi Products
            </h1>
            <p className="text-base text-muted-foreground">
            Support Small businesses, Be part of India&apos;s Growth Story.
            </p>
            <div className="flex flex-col gap-6">
              <Button size="lg" className="mx-auto text-white bg-black hover:bg-black/90 rounded-full">
                Download the App
                <ArrowUpRight className="ml-2 h-4 w-4" />
              </Button>
              <Link
                href="/business"
                className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground mx-auto justify-center"
              >
                or Sell Your Products
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link> 
            </div>
            <div className="mt-8 py-20">
              
                <Image
                  src="/product-shots/swadesic-mockup.png"
                  alt="Store Products Feed"
                  width={1920}
                  height={1080}
                  className="w-full h-auto"
                  priority
                />
              
            </div>
          </div>
        </div>
        

        {/* Buyer Features Section */}
        <FeaturesSectionAdaptive
          title={buyerFeatures.title}
          description={buyerFeatures.description}
          features={buyerFeatures.features}
          className="bg-gray-50"
        />

        <FeaturesSectionResponsiveImage
          feature={{
            title: "Track Every Order Status",
            description: "No more waiting for order update on DMs. Get notified with every order update, from confirmation to delivery and refunds if any including disputes.",
            // image: "/Swadesic-web-prep/order/every-orders-statuses-covered.jpg",
            mobileImage: "/Swadesic-web-prep/order/every-orders-statuses-covered-mobile.jpg",
            desktopImage: "/Swadesic-web-prep/order/every-orders-statuses-covered-desktop.jpg"
          }}  
          className="bg-white"
        />

        {/* Impact Story Section */}
        <section className="py-24 sm:py-32">
          <div className="mx-auto max-w-7xl px-2 lg:px-2">
            {/* Header Section */}
            <div className="mx-auto max-w-2xl text-center mb-16">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Small Swadeshi Choice! Big Impact!
              </h2>
              <p className="mt-4 text-lg leading-8 text-muted-foreground">
                How choosing Swadeshi Products bring Self-reliance to our nation.
              </p>
            </div>

            {/* Initial Choice */}
            <div className="max-w-4xl mx-auto mb-16">
              <div className="bg-white rounded-2xl shadow-lg p-8 sm:p-12">
                <div className="flex flex-col gap-6">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center shrink-0">
                      <ShoppingBag className="w-8 h-8 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-semibold">Imagine you need shoes.</h3>
                      <p className="text-muted-foreground mt-2">You have two options:</p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 rounded-lg bg-red-50">
                      <span className="font-medium">Imported Brand</span>
                      <span className="text-lg font-semibold">₹1,200</span>
                    </div>
                    <div className="flex items-center justify-between p-4 rounded-lg bg-green-50">
                      <span className="font-medium">Swadeshi Brand</span>
                      <span className="text-lg font-semibold">₹1,500</span>
                    </div>
                    <p className="text-muted-foreground pt-2 flex items-center">
                      At first, the imported shoes seem cheaper. But here&apos;s what happens when you choose Swadeshi...
                    </p>
                    <div className="flex justify-center">
                      <ArrowDown className="h-5 w-5" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Story Phases */}
            <div className="max-w-5xl mx-auto mb-16">
              <StoryCarousel phases={storyPhases} />
            </div>

            {/* Message Section */}
            <div className="max-w-4xl mx-auto mt-16 space-y-8">
              {/* The Alternative - Now in Positive Light */}
              <div className="bg-primary/5 rounded-2xl p-8 sm:p-12">
                <div className="flex flex-col gap-6">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center shrink-0">
                      <Globe className="w-8 h-8 text-primary" />
                    </div>
                    <h3 className="text-xl font-semibold">What If you Choose Non-Swadeshi Brand?</h3>
                  </div>
                  <p className="text-muted-foreground">
                    Choosing imported shoes means your ₹1,200 goes abroad (excluding taxes + nominal costs). 
                    The local market misses out on that money, limiting growth and innovation.
                  </p>
                </div>
              </div>

              {/* The Bigger Picture */}  
              <div className="bg-primary/10 rounded-2xl p-8 sm:p-12">
                <div className="flex flex-col gap-6">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 rounded-xl bg-primary/20 flex items-center justify-center shrink-0">
                      <LineChart className="w-8 h-8 text-primary" />
                    </div>
                    <h3 className="text-xl font-semibold">Swadeshi Change: The Bigger Picture</h3>
                  </div>
                  <p className="text-muted-foreground">
                  Your Swadeshi purchase keeps money inside the nation, improving the production, creating more jobs and strengthening the economy. As more people choose Swadeshi, prices fall, quality improves, and the nation becomes self-reliant. And <b>you buy the same shoes for ₹1000 two years later.</b>
                  </p>
                </div>
              </div>

              {/* Final Impact - Simplified */}
              <div className="bg-gradient-to-br from-primary to-primary/90 text-primary-foreground rounded-2xl items-center p-8 sm:p-12">
                <div className="text-center mb-12 gap-10">
                  <h3 className="text-2xl sm:text-2xl font-medium">The Final Impact</h3>
                  <p className="text-base font-bold md:text-lg font-bold text-primary-foreground mt-4">
                    Get the shoes down to ₹1000
                    </p>
                </div>
                
                <div className=" bg-white flex flex-col sm:flex-row items-center justify-center max-w-5xl mx-auto space-y-6 sm:space-y-0 sm:space-x-12">
                  {/* Box 1 */}
                  <div className="bg-gradient-to-br from-primary/20 to-primary/10 backdrop-blur-sm rounded-2xl p-8 w-full sm:w-2/5 text-center transform hover:scale-105 transition-transform duration-300 shadow-lg">
                    <p className="text-md md:text-xl font-semibold text-primary-foreground">
                      Your Today&apos;s Swadeshi Choice 
                    </p>
                  </div>

                  {/* Equals Symbol */}
                  <div className="text-4xl sm:text-5xl font-bold text-primary-foreground animate-pulse">=</div>

                  {/* Box 2 */}
                  <div className="bg-gradient-to-br from-primary/20 to-primary/10 backdrop-blur-sm rounded-2xl p-8 w-full sm:w-2/5 text-center transform hover:scale-105 transition-transform duration-300 shadow-lg">
                    <p className="text-md md:text-xl font-semibold text-primary-foreground">
                      Creates a Stronger and<br />Self-Reliant Bharat
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <FAQSection
          title="Frequently Asked Questions"
          description="Everything you need to know about shopping on Swadesic"
          faqs={buyerFAQs}
        />
      </main>
    </div>
  )
}