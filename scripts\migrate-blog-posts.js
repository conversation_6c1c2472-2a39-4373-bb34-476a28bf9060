const fs = require('fs')
const path = require('path')
const { blogPosts } = require('../data/blog-data.ts')

// Function to convert blog post to MDX format
function convertToMDX(post) {
  const frontmatter = `---
title: "${post.title}"
slug: "${post.slug}"
excerpt: "${post.excerpt}"
coverImage: "${post.coverImage}"
date: "${post.date}"
author: "${post.author.name.toLowerCase().replace(/\s+/g, '-')}"
categories: ${JSON.stringify(post.categories)}
tags: ${JSON.stringify(post.tags)}
readingTime: "${post.readingTime}"
seo:
  metaTitle: "${post.title} | Swadesic Blog"
  metaDescription: "${post.excerpt}"
  keywords: ${JSON.stringify(post.tags)}
---

${post.content.trim()}`

  return frontmatter
}

// Function to create author file if it doesn't exist
function createAuthorFile(author) {
  const authorSlug = author.name.toLowerCase().replace(/\s+/g, '-')
  const authorPath = path.join(__dirname, '../content/authors', `${authorSlug}.json`)
  
  if (!fs.existsSync(authorPath)) {
    const authorData = {
      name: author.name,
      image: author.image,
      bio: `${author.name} is a contributor to the Swadesic blog.`,
      social: {}
    }
    
    fs.writeFileSync(authorPath, JSON.stringify(authorData, null, 2))
    console.log(`Created author file: ${authorPath}`)
  }
}

// Function to get file path for blog post
function getPostPath(post) {
  const date = new Date(post.date)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  
  const dir = path.join(__dirname, '../content/blog', String(year), month)
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }
  
  return path.join(dir, `${post.slug}.mdx`)
}

// Main migration function
function migrateBlogPosts() {
  console.log('Starting blog post migration...')
  
  blogPosts.forEach((post, index) => {
    try {
      // Create author file
      createAuthorFile(post.author)
      
      // Convert to MDX
      const mdxContent = convertToMDX(post)
      
      // Get file path
      const filePath = getPostPath(post)
      
      // Check if file already exists
      if (fs.existsSync(filePath)) {
        console.log(`Skipping ${post.slug} - file already exists`)
        return
      }
      
      // Write MDX file
      fs.writeFileSync(filePath, mdxContent)
      console.log(`✅ Migrated: ${post.slug}`)
      
    } catch (error) {
      console.error(`❌ Error migrating ${post.slug}:`, error.message)
    }
  })
  
  console.log('Migration completed!')
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateBlogPosts()
}

module.exports = { migrateBlogPosts }
