import { Container } from "@/components/ui/container";
import { Metadata } from "next";
import Link from "next/link";
import { ArrowRight } from "lucide-react";

export const metadata: Metadata = {
  title: "Policies | Swadesic",
  description: "Swadesic policies, terms, and guidelines",
};

const policies = [
  {
    title: "Privacy Policy",
    description: "Learn how we collect, use, and protect your personal information.",
    href: "/policies/privacy-policy",
  },
  {
    title: "Terms and Conditions",
    description: "Understand the rules and regulations for using Swadesic platform.",
    href: "/policies/terms",
  },
  {
    title: "Seller Terms and Conditions",
    description: "Specific terms and conditions for Swadesic sellers and business partners.",
    href: "/policies/seller-terms",
  },
  {
    title: "Community Guidelines",
    description: "Guidelines for maintaining a positive and respectful community on Swadesic.",
    href: "/policies/community-guidelines",
  },
];

export default function PoliciesPage() {
  return (
    <Container as="main" className="py-12 sm:py-16">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl mb-4">
            Swadesic Policies
          </h1>
          <p className="text-lg text-muted-foreground">
            Our commitment to transparency, privacy, and fair business practices
          </p>
        </div>

        {/* Policy Cards */}
        <div className="grid gap-6">
          {policies.map((policy) => (
            <Link
              key={policy.href}
              href={policy.href}
              className="group block"
            >
              <div className="rounded-lg border bg-card p-6 shadow-sm transition-shadow hover:shadow-md">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                      {policy.title}
                    </h2>
                    <p className="text-muted-foreground">
                      {policy.description}
                    </p>
                  </div>
                  <ArrowRight className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Contact Section */}
        <div className="mt-12 text-center">
          <p className="text-muted-foreground">
            Have questions about our policies? {" "}
            <a
              href="mailto:<EMAIL>"
              className="text-primary hover:underline"
            >
              Contact our support team
            </a>
          </p>
        </div>
      </div>
    </Container>
  );
}
