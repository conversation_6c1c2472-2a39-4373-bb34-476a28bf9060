import { Asterisk, Check, Sparkles, Rocket, Shield, Scale, Target } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

const plans = [
  {
    name: "Unlimited Growth Plan",
    forWhomPrimary: "For high-volume stores looking to scale.",
    forWhomSecondary: "Best for Growing Stores",
    price: "₹10,000",
    interval: "month",
    cta: "Get started with Unlimited",
    benefits: [
      "Everything of Free Plan",
      "Provide limited access to your staff via Staff accounts to help you run operations",
      "Customizable store superlink for easy sharing on social media",
      "Premium Badge displayed next to your storename",
      "Early access to the latest features for marketing, analytics, and branding tools"
    ]
  },
  {
    name: "Free Plan",
    forWhomPrimary: "A zero-risk plan to get started and pay only when you sell.",
    forWhomSecondary: "Most Popular for New Sellers",
    price: "₹0",
    interval: "month",
    popular: true,
    cta: "Start Selling for Free",
    benefits: [
      "Professional Storefront with Customer Community",
      "Unlimited products, Unlimited posts and Unlimited orders",
      "₹59 handling fee (inclusive of GST), Only pay when you make a sale",
      "₹250 Flash points added monthly making 5 orders free every month",
      "Full access to core store management, community building, and customer engagement features for building loyal customer base",
      "Secure payment processing with 2-day settlements from payout request",
      "In-app messaging for direct communication with customers",
      "Store updates group for announcements to your community",
      "Customizable store superlink for easy sharing on social media",
    ]
  }
]

export default function PricingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        {/* Pricing Cards Section */}
        <section className="container py-8 md:py-12 lg:py-24">
          <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
            <h1 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
              Simple, Transparent Pricing
            </h1>
            <p className="max-w-[85%] leading-normal text-gray-600 sm:text-lg sm:leading-7">
              Choose the perfect plan for your business growth
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 mx-auto max-w-5xl pt-8">
            {plans.map((plan) => (
              <div
                key={plan.name}
                className={`relative flex flex-col rounded-2xl border-2 bg-white p-6 shadow-lg ${
                  !plan.popular ? "border-[var(--brand-color)]" : "border-gray-900"
                }`}
              >
                {!plan.popular && (
                  <div className="absolute -top-5 left-0 right-0 mx-auto w-fit rounded-full bg-[var(--brand-color)] px-3 py-1 text-sm font-semibold text-white">
                    Recommended
                  </div>
                )}
                <div className="space-y-4">
                  <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                  <p className="text-gray-600 text-sm">{plan.forWhomPrimary}</p>
                  <p className="text-gray-900 font-semibold">{plan.forWhomSecondary}</p>
                  <div className="flex items-baseline">
                    <span className="text-3xl font-bold text-gray-900">{plan.price}</span>
                    <span className="text-gray-600 ml-1">/{plan.interval}</span>
                  </div>
                </div>
                <div className="mt-8">
                  <Button 
                    className={`w-full ${
                      !plan.popular 
                        ? "bg-[var(--brand-color)] border-2 border-[var(--brand-color)] text-white hover:bg-[var(--brand-color)]/90" 
                        : "bg-white text-black border-2 border-black hover:bg-black/5"
                    }`}
                    asChild
                  >
                    <a 
                      href="https://play.google.com/store/apps/details?id=com.sociallyx.swadesic" 
                      target="_blank" 
                      rel="noopener noreferrer"
                    >
                      {plan.cta}
                    </a>
                  </Button>
                </div>
                <ul className="mt-8 space-y-4">
                  {plan.benefits.map((benefit) => (
                    <li key={benefit} className="flex items-start gap-4">
                      <Asterisk className="h-5 w-5 flex-shrink-0 text-gray-900" />
                      <span className="text-black text-sm">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </section>

        {/* Special Offer Section */}
        <section className="w-full bg-gradient-to-b from-[var(--brand-color)]/10 to-[var(--brand-color)]/20">
          <div className="container py-16">
            <div className="mx-auto max-w-5xl">
              <div className="rounded-3xl border-2 border-[var(--brand-color)] bg-white p-8 text-center shadow-xl">
                <div className="flex justify-center mb-4">
                  <Sparkles className="h-8 w-8 text-[var(--brand-color)]" />
                </div>
                <h2 className="text-2xl font-bold text-[var(--brand-color)]">Limited Time Launch Offer</h2>
                <p className="text-xl font-semibold mt-4 text-gray-900">Get 100x6 = 600 Free Orders for First 6 Months</p>
                <p className="text-sm text-gray-600 mt-2">
                  **Only for Stores created &amp; verified before December 15th, 2024
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Flat Fee vs Percentage Section */}
        <section className="container py-16 md:py-24">
          <div className="mx-auto max-w-[85rem] grid gap-12 lg:grid-cols-2 lg:gap-16 items-center ">
            <div className="space-y-8 px-4 md:px-8">
              <h2 className="text-3xl md:text-4xl font-bold leading-tight">
                Goodbye to Profit-Eating Percentages &amp; Subscriptions
              </h2>
              <p className="text-base text-black">
                It doesn&apos;t make sense to charge more when it doesn&apos;t take extra servers to process an order of ₹5000 against ₹500 other than just one more zero and payment processing fee. By choosing the Free Plan, enjoy the Simple Flat Fee Pricing.
              </p>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="flex gap-2 items-start">
                  <div className="mt-1 p-1 rounded-lg bg-green-100">
                    <Check className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Predictable Costs</h3>
                    <p className="text-sm text-gray-600">Know exactly what you&apos;ll pay per order</p>
                  </div>
                </div>
                <div className="flex gap-2 items-start">
                  <div className="mt-1 p-1 rounded-lg bg-green-100">
                    <Check className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Keep More Profits</h3>
                    <p className="text-sm text-gray-600">No percentage cuts from your margins</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="mx-auto lg:max-w-[80%]">
              <Image
                src="/explanatory/percentage-vs-flat.png"
                alt="Flat Fee vs Percentage Pricing Comparison"
                width={1200}
                height={675}
                className="rounded-2xl shadow-xl w-full px-2 py-2"
              />
            </div>
          </div>
        </section>

        {/* Why Choose Section */}
        <section className="w-full bg-gray-100">
          <div className="container py-24">
            <div className="mx-auto max-w-5xl">
              <div className="grid gap-16 lg:grid-cols-2">
                {/* Why Start with Free Plan */}
                <div className="space-y-12">
                  <h2 className="text-3xl font-bold text-gray-900 text-center lg:text-left">
                    Why Start with the Free Plan?
                  </h2>
                  
                  <div className="space-y-8">
                    <div className="flex gap-4">
                      <Shield className="h-6 w-6 flex-shrink-0 text-[var(--brand-color)]" />
                      <div>
                        <h3 className="font-semibold mb-2 text-gray-900">Zero-Risk Start</h3>
                        <p className="text-gray-600">With no upfront cost, no subscriptions, and only paying for sales, the Free Plan is ideal for new sellers. Plus, you get 5 free orders every month to kickstart your journey.</p>
                      </div>
                    </div>

                    <div className="flex gap-4">
                      <Scale className="h-6 w-6 flex-shrink-0 text-[var(--brand-color)]" />
                      <div>
                        <h3 className="font-semibold mb-2 text-gray-900">Flexible Growth Path</h3>
                        <p className="text-gray-600">Start with the Free Plan, and scale at your own pace. As your store grows, you can easily transition to the Unlimited Growth Plan if you want to eliminate per-order fees and access additional advanced tools.</p>
                      </div>
                    </div>

                    <div className="flex gap-4">
                      <Target className="h-6 w-6 flex-shrink-0 text-[var(--brand-color)]" />
                      <div>
                        <h3 className="font-semibold mb-2 text-gray-900">Designed for Every Seller</h3>
                        <p className="text-gray-600">Whether you&apos;re just starting out or already have a high-volume business, Swadesic has a plan that fits your needs.</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Why Unlimited Growth Plan */}
                <div className="space-y-12">
                  <h2 className="text-3xl font-bold text-gray-900 text-center lg:text-left">
                    Why the Unlimited Growth Plan?
                  </h2>
                  
                  <div className="flex gap-4">
                    <Rocket className="h-6 w-6 flex-shrink-0 text-[var(--brand-color)]" />
                    <div>
                      <p className="text-gray-600">When you&apos;re ready to scale, the Unlimited Growth Plan offers the most powerful tools and features for growing businesses. If you&apos;re already seeing success, this plan eliminates per-order fees and gives you access to the highest level of support and functionality.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}
