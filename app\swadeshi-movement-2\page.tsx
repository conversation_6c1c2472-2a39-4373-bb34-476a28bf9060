import { Container } from "@/components/ui/container"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { ArrowUpRight, Instagram, Twitter, Youtube } from "lucide-react"

export default function SwadeshiMovement2() {
  return (
    <Container as="main" className="py-12 sm:py-16">
      <article className="prose prose-lg dark:prose-invert mx-auto px-2 md:px-32">
        <h1 className="text-4xl font-bold mb-8 text-center">
          Support Swadeshi Movement 2.0
        </h1>

        <div className="text-lg text-muted-foreground mb-12">
          In the early 1900s, the Swadeshi Movement became a symbol of India&apos;s fight for independence. Today, we continue this legacy through Swadeshi Movement 2.0 - a battle for economic and technological sovereignty.
        </div>

        <section className="mb-12">
          <div className="rounded-2xl overflow-hidden flex flex-col">
            <div className="aspect-[16/9] relative">
              <Image
                src="/Assets/lal-bal-pal full.jpg"
                alt="<PERSON> - Leaders of the Swadeshi Movement"
                fill
                className="object-contain bg-gray-100"
              />
            </div>
            <div className="bg-black/80 text-white p-4 md:p-6 text-sm">
              Lal Bal Pal (Lala Lajpat Rai, Bal Gangadhar Tilak, and Bipin Chandra Pal) were the triumvirate of assertive nationalists who advocated the Swadeshi movement.
            </div>
          </div>

          <h2 className="text-2xl font-semibold mb-4 mt-12">Reclaiming India&apos;s Independence</h2>
          <p>
            In the early 1900s, the Swadeshi Movement became a symbol of India&apos;s fight for independence. It was a battle for freedom—not just from British rule, but from the economic and cultural dominance of foreign powers.
          </p>
          <p>
            The movement called for the boycott of foreign goods and the promotion of indigenous industries, aiming to restore self-reliance, pride, and sovereignty to India. Fast forward to today, and we find ourselves in the middle of a new battle for India&apos;s Non-political Independence—Swadeshi Movement 2.0.
          </p>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Why This Movement Matters Today</h2>
          <p>
            India, for all its growth, continues to be a nation heavily dependent on foreign technology, businesses, and industries. While we have made significant progress in some areas, we still rely on external forces for our technological advancements, agricultural imports, and even critical infrastructure like defence and energy security.
          </p>
          <p>
            This dependence leaves us vulnerable—not just economically, but existentially as well. We are not self-reliant enough to lead our own strategically. The global power dynamics and geopolitical shifts of the 21st century highlight the urgency of reclaiming control over our destiny.
          </p>
          <p>
            Swadeshi Movement 2.0 is more than a call for the support of local businesses; it is a call to action to regain control of our technological, economic, and cultural future. This isn&apos;t just about buying products from small businesses; it&apos;s about building an ecosystem where India thrives on its own innovation, resources, and technology.
          </p>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">The Historical Context</h2>
          <p>
            The Swadeshi Movement of the 20th century was born as a response to this—a movement led by brave souls like Lala Lajpat Rai, Bal Gangadhar Tilak, and Bipin Chandra Pal, who urged the masses to support Indian-made goods over foreign imports.
          </p>
          <p>
            Their call wasn&apos;t just about buying handwoven khadi or celebrating local industries; it was a declaration of independence—a rejection of foreign exploitation. This was a fight to build India&apos;s future on Indian terms, with Indian resources, Indian labor, and Indian ingenuity. And it worked. It inspired a sense of unity, pride, and self-belief in the Indian masses that played a vital role in the ultimate fight for freedom.
          </p>
          <p>
            The Swadeshi Movement 2.0 is the continuation of that same fight—but in the context of technology, innovation, and economic sovereignty. Today, India imports massive amounts of foreign technology—from mobile phones and software to critical defense systems. The dominance of foreign tech giants limits the scope for Indian entrepreneurs and startups to flourish. This isn&apos;t just a trade issue; it&apos;s a national security issue.
          </p>
        </section>

        <section className="mb-12 bg-black text-white p-8 rounded-2xl">
          <h2 className="text-2xl font-semibold mb-4 text-center">Join the Movement</h2>
          <p className="text-xl text-center mb-8">
            &quot;Bharat is calling for her noblest sons & daughters. Will you answer the call?&quot;
            <br />
            <span className="text-sm block mt-2">- Inspired by Netaji Subhas Chandra Bose</span>
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Button asChild size="lg" className="bg-white text-black hover:bg-white/90 rounded-full">
              <Link 
                href="https://play.google.com/store/apps/details?id=com.sociallyx.swadesic"
                target="_blank"
                rel="noopener noreferrer"
                className="gap-2"
              >
                Download Swadesic App
                <ArrowUpRight className="h-4 w-4" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="text-white hover:text-white rounded-full">
              <Link href="/contact" className="gap-2">
                Join Our Network
                <ArrowUpRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4 text-center">Stay Connected</h2>
          <p className="text-lg text-muted-foreground text-center mb-8">
            Follow us on social media to stay informed and inspired
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Button asChild variant="outline" size="lg" className="rounded-full">
              <Link 
                href="https://instagram.com/swadesic" 
                target="_blank"
                rel="noopener noreferrer"
                className="gap-2"
              >
                <Instagram className="h-5 w-5" />
                Follow on Instagram
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="rounded-full">
              <Link 
                href="https://x.com/SwadesicApp" 
                target="_blank"
                rel="noopener noreferrer"
                className="gap-2"
              >
                <Twitter className="h-5 w-5" />
                Follow on Twitter
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="rounded-full">
              <Link 
                href="https://www.youtube.com/@swadesic" 
                target="_blank"
                rel="noopener noreferrer"
                className="gap-2"
              >
                <Youtube className="h-5 w-5" />
                Subscribe on YouTube
              </Link>
            </Button>
          </div>
        </section>
      </article>
    </Container>
  )
}
