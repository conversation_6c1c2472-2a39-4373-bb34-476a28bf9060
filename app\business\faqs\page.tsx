import fs from 'fs'
import path from 'path'
import { parseFAQs } from "@/lib/parse-faqs"
import FAQSection from "@/components/faq-section"

export default function BusinessFAQsPage() {
  // Read the markdown file
  const markdownPath = path.join(process.cwd(), 'public', 'Markdowns', 'seller', 'complete-faqs.md')
  const markdown = fs.readFileSync(markdownPath, 'utf8')
  const faqs = parseFAQs(markdown)
  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1 py-8">
        <div className="mx-auto max-w-3xl text-center">
          <h1 className="font-heading text-3xl sm:text-4xl md:text-5xl lg:text-6xl leading-tight mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Find answers to common questions about selling on Swadesic
          </p>
        </div>
        <div className="mx-auto max-w-4xl">
          <FAQSection faqs={faqs} />
        </div>
      </main>
    </div>
  )
}
