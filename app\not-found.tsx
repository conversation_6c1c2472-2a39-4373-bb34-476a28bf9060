import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Container } from "@/components/ui/container"
import { Mail, Store, Home, ArrowUpRight, MessageCircle } from "lucide-react"

export default function NotFound() {
  return (
    <Container>
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-4rem)] py-16 text-center">
        {/* Header */}
        <div className="space-y-4 mb-8">
          <h1 className="text-4xl font-medium tracking-tight">
            Aiyyo! Looks Like This Page Took the Wrong Train! 🚂
          </h1>
          <p className="text-lg font-medium text-muted-foreground">
            But don&apos;t worry, you&apos;ve arrived at the right platform – Swadesic!
          </p>
          <p className="text-base text-muted-foreground max-w-2xl mx-auto">
            The page you&apos;re searching for might have been moved, renamed, or never existed (like the last bite of your samosa).
          </p>
        </div>

        {/* Actions */}
        <div className="grid gap-4 sm:grid-cols-2 max-w-2xl mx-auto mb-12">
          <Link 
            href="https://swadesic.com"
            className="flex flex-col p-6 rounded-xl border bg-card text-card-foreground shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground text-left"
          >
            <div className="flex items-center gap-2 mb-2">
              <Store className="h-5 w-5 flex-shrink-0 text-[var(--brand-color)]" />
              <div className="font-medium">Browse Swadeshi Products</div>
            </div>
            <div className="text-sm text-muted-foreground">
              Discover authentic and proudly Indian items
            </div>
          </Link>

          <Link 
            href="https://play.google.com/store/apps/details?id=com.sociallyx.swadesic&hl=en_SG"
            className="flex flex-col p-6 rounded-xl border bg-card text-card-foreground shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground text-left"
          >
            <div className="flex items-center gap-2 mb-2">
              <Store className="h-5 w-5 flex-shrink-0 text-[var(--brand-color)]" />
              <div className="font-medium">Download App</div>
            </div>
            <div className="text-sm text-muted-foreground">
              Get the Swadesic app on your phone
            </div>
          </Link>
        </div>

        {/* Help Section */}
        <div className="text-center max-w-[800px] mx-auto">
          <h2 className="text-xl font-medium mb-6">Need Help?</h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Button asChild variant="outline" size="lg" className="rounded-full">
              <Link 
                href="https://wa.me/918331086680"
                target="_blank"
                rel="noopener noreferrer"
                className="gap-2"
              >
                <MessageCircle className="h-5 w-5" />
                Chat on WhatsApp
                <ArrowUpRight className="h-4 w-4" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="rounded-full">
              <Link href="mailto:<EMAIL>" className="gap-2">
                <Mail className="h-5 w-5" />
                Email Support
                <ArrowUpRight className="h-4 w-4" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="rounded-full">
              <Link href="/" className="gap-2">
                <Home className="h-5 w-5" />
                Back to Home
                <ArrowUpRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </Container>
  )
}
