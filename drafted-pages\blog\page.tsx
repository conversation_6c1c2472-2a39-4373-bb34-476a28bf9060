import { blogPosts, categories, tags } from "@/data/blog-data"
import { BlogCard } from "@/components/blog/blog-card"
import { BlogSidebar } from "@/components/blog/blog-sidebar"
import { SearchBar } from "@/components/blog/search-bar"
import { searchPosts } from "@/lib/search"
import { BlogPost } from "@/types/blog"
import { Container } from "@/components/ui/container"

interface BlogPageProps {
  searchParams: { [key: string]: string | string[] | undefined }
}

export default function BlogPage({ searchParams }: BlogPageProps) {
  const query = typeof searchParams.q === "string" ? searchParams.q : ""
  const filteredPosts = query ? searchPosts(blogPosts, query) : blogPosts

  return (
    <Container as="main" className="py-8 sm:py-12">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-9">
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-3xl font-bold tracking-tight">Latest Articles</h1>
              <p className="text-lg text-muted-foreground mb-8">
                Explore insights on &quot;Being Swadeshi&quot; and building a stronger local economy.
              </p>
              <SearchBar />
            </div>

            {query && (
              <p className="text-sm text-muted-foreground">
                Found {filteredPosts.length} result{filteredPosts.length === 1 ? "" : "s"} for "{query}"
              </p>
            )}

            <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredPosts.map((post: BlogPost) => (
                <BlogCard key={post.id} post={post} />
              ))}
              {filteredPosts.length === 0 && (
                <div className="col-span-full text-center py-12">
                  <p className="text-muted-foreground">No posts found. Try a different search term.</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-3">
          <div className="sticky top-24">
            <BlogSidebar categories={categories} tags={tags} />
          </div>
        </div>
      </div>
    </Container>
  )
}
