"use client"

import { useTheme } from "next-themes"
import { useEffect, useRef } from "react"

interface CommentsProps {
  title: string
}

export function Comments({ title }: CommentsProps) {
  const { theme } = useTheme()
  const commentsRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Load Giscus
    const script = document.createElement("script")
    script.src = "https://giscus.app/client.js"
    script.setAttribute("data-repo", "your-github-username/your-repo-name") // Replace with your repo
    script.setAttribute("data-repo-id", "your-repo-id") // Replace with your repo ID
    script.setAttribute("data-category", "Blog Comments")
    script.setAttribute("data-category-id", "your-category-id") // Replace with your category ID
    script.setAttribute("data-mapping", "title")
    script.setAttribute("data-strict", "1")
    script.setAttribute("data-reactions-enabled", "1")
    script.setAttribute("data-emit-metadata", "0")
    script.setAttribute("data-input-position", "top")
    script.setAttribute("data-theme", theme === "dark" ? "dark" : "light")
    script.setAttribute("data-lang", "en")
    script.crossOrigin = "anonymous"
    script.async = true

    const comments = commentsRef.current
    if (comments) {
      comments.innerHTML = ""
      comments.appendChild(script)
    }

    return () => {
      if (comments) {
        comments.innerHTML = ""
      }
    }
  }, [theme, title])

  return (
    <section className="mt-16 pt-8 border-t">
      <h2 className="text-2xl font-bold mb-8">Comments</h2>
      <div ref={commentsRef} />
    </section>
  )
}
