import { Metadata } from "next"
import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { getAuthorData, getPostsByAuthor, getAllBlogPosts } from "@/lib/mdx"
import { InfiniteScroll } from "@/components/blog/infinite-scroll"
import { ExternalLink } from "lucide-react"
import type { BlogPost } from "@/types/blog"

interface AuthorPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: AuthorPageProps): Promise<Metadata> {
  const authorData = getAuthorData(params.slug)

  if (!authorData) {
    return {
      title: "Author Not Found",
    }
  }

  return {
    title: `${authorData.name} | Swadesic Blog Authors`,
    description: authorData.bio || `Read articles by ${authorData.name} on the Swadesic blog.`,
    openGraph: {
      title: `${authorData.name} | Swadesic Blog Authors`,
      description: authorData.bio || `Read articles by ${authorData.name} on the Swadesic blog.`,
      images: [
        {
          url: authorData.image,
          width: 400,
          height: 400,
          alt: authorData.name,
        },
      ],
    },
  }
}

export async function generateStaticParams() {
  const posts = await getAllBlogPosts()
  const authorSlugs = new Set<string>()

  posts.forEach((post: BlogPost) => {
    if (!post.author) return;
    const authorSlug = post.author.name.toLowerCase().replace(/\s+/g, '-')
    authorSlugs.add(authorSlug)
  })

  return Array.from(authorSlugs).map((slug) => ({
    slug,
  }))
}

export default async function AuthorPage({ params }: AuthorPageProps) {
  const authorData = getAuthorData(params.slug)

  if (!authorData) {
    notFound()
  }

  // Get all posts by this author
  const authorPosts = await getPostsByAuthor(params.slug)
  const initialPosts = authorPosts.slice(0, 12) // First page

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Author Header */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
            {/* Author Image */}
            <div className="w-32 h-32 rounded-full overflow-hidden flex-shrink-0">
              <Image
                src={authorData.image}
                alt={authorData.name}
                width={128}
                height={128}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Author Info */}
            <div className="flex-1 text-center md:text-left">
              <h1 className="text-3xl font-bold mb-4 text-foreground">
                {authorData.name}
              </h1>

              {authorData.bio && (
                <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                  {authorData.bio}
                </p>
              )}

              {/* Social Links */}
              {authorData.social && (
                <div className="flex flex-wrap justify-center md:justify-start gap-4">
                  {authorData.social.twitter && (
                    <a
                      href={`https://twitter.com/${authorData.social.twitter.replace('@', '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                      Twitter
                    </a>
                  )}
                  {authorData.social.linkedin && (
                    <a
                      href={`https://linkedin.com/${authorData.social.linkedin}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                      LinkedIn
                    </a>
                  )}
                  {authorData.social.github && (
                    <a
                      href={`https://github.com/${authorData.social.github}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                      GitHub
                    </a>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Posts by Author */}
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-2xl font-bold text-foreground">
              Articles by {authorData.name}
            </h2>
            <span className="text-muted-foreground">
              {authorPosts.length} {authorPosts.length === 1 ? 'article' : 'articles'}
            </span>
          </div>

          <InfiniteScroll
            initialPosts={initialPosts}
            author={params.slug}
          />
        </div>

        {/* Back to Blog */}
        <div className="max-w-4xl mx-auto mt-12 text-center">
          <Link
            href="/blog"
            className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
          >
            ← Back to all articles
          </Link>
        </div>
      </div>
    </div>
  )
}
