import { NextRequest, NextResponse } from 'next/server'
import { getPublishedBlogPosts, searchPosts } from '@/lib/mdx'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!query.trim()) {
      return NextResponse.json({
        results: [],
        total: 0,
        query: '',
      })
    }

    // Get all published posts
    const allPosts = await getPublishedBlogPosts()
    
    // Search posts
    const searchResults = searchPosts(allPosts, query)
    
    // Limit results for performance
    const limitedResults = searchResults.slice(0, limit)

    return NextResponse.json({
      results: limitedResults,
      total: searchResults.length,
      query,
    })
  } catch (error) {
    console.error('Error searching blog posts:', error)
    return NextResponse.json(
      { error: 'Failed to search blog posts' },
      { status: 500 }
    )
  }
}
