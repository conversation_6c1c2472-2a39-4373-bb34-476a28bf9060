# How to Add Blog Posts

This guide explains how to create and manage blog posts in the Swadesic MDX-based blog system.

## Quick Start

1. Create a new `.mdx` file in the appropriate date folder
2. Add frontmatter with post metadata
3. Write your content in Markdown
4. Save and the post will automatically appear on the blog

## File Structure

```
content/
├── blog/
│   ├── 2024/
│   │   ├── 12/
│   │   │   ├── whats-new-swadesic-december-15.mdx
│   │   │   └── trending-products-sell-online-2025.mdx
│   │   └── 11/
│   │       └── tech-sovereignty-the-new-national-security.mdx
│   └── 2025/
│       └── 01/
│           └── your-new-post.mdx
├── authors/
│   ├── swadesic-team.json
│   └── author-name.json
└── images/
    ├── covers/
    └── authors/
```

## Step-by-Step Guide

### 1. Create the File Structure

Create folders for the year and month if they don't exist:
```
content/blog/YYYY/MM/your-post-slug.mdx
```

**Example:**
```
content/blog/2024/12/my-awesome-post.mdx
```

### 2. Add Frontmatter

Every blog post must start with frontmatter (metadata) between `---` markers:

```yaml
---
title: "Your Post Title"
slug: "your-post-slug"
excerpt: "A brief description of your post that appears in previews and meta descriptions."
coverImage: "https://example.com/image.jpg"
date: "2024-12-20"
author: "author-slug"
categories: ["Category 1", "Category 2"]
tags: ["tag1", "tag2", "tag3"]
readingTime: "5 min read"
seo:
  metaTitle: "SEO Optimized Title | Swadesic Blog"
  metaDescription: "SEO optimized description for search engines."
  keywords: ["keyword1", "keyword2", "keyword3"]
---
```

### 3. Write Your Content

After the frontmatter, write your content in Markdown:

```markdown
# Your Main Heading

This is your introduction paragraph.

## Section Heading

Your content here with **bold text** and *italic text*.

### Subsection

- Bullet point 1
- Bullet point 2
- Bullet point 3

1. Numbered list item
2. Another item
3. Final item

> This is a blockquote for important information.

```code
This is inline code
```

## Frontmatter Fields Reference

### Required Fields

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `title` | string | Post title | `"How to Build a Swadeshi Business"` |
| `slug` | string | URL-friendly identifier | `"how-to-build-swadeshi-business"` |
| `excerpt` | string | Brief description | `"Learn the fundamentals..."` |
| `coverImage` | string | Featured image URL | `"https://example.com/image.jpg"` |
| `date` | string | Publication date (YYYY-MM-DD) | `"2024-12-20"` |
| `author` | string | Author slug | `"swadesic-team"` |
| `categories` | array | Post categories | `["Business", "Entrepreneurship"]` |
| `tags` | array | Post tags | `["startup", "local-business"]` |

### Optional Fields

| Field | Type | Description | Default |
|-------|------|-------------|---------|
| `status` | string | Post status: `"draft"` or `"published"` | `"published"` |
| `readingTime` | string | Estimated reading time | Auto-calculated |
| `seo.metaTitle` | string | Custom SEO title | Uses post title |
| `seo.metaDescription` | string | Custom meta description | Uses excerpt |
| `seo.keywords` | array | SEO keywords | Empty array |

## Draft and Published Posts

### Publishing Status

Posts can have two statuses:
- **`published`** (default): Post is visible to all users and included in SEO
- **`draft`**: Post is hidden from public view and excluded from SEO

### Creating Draft Posts

To create a draft post, add `status: "draft"` to your frontmatter:

```yaml
---
title: "My Draft Post"
slug: "my-draft-post"
excerpt: "This is a draft post."
# ... other fields
status: "draft"
---
```

### Previewing Draft Posts

Draft posts can be previewed by admins using the preview parameter:
```
https://yoursite.com/blog/your-draft-slug?preview=true
```

### Draft Post Behavior

- **Public Pages**: Draft posts are excluded from blog listings, category pages, author pages, and search results
- **SEO**: Draft posts have `noindex, nofollow` meta tags to prevent search engine indexing
- **Preview Mode**: Draft posts show a yellow banner indicating they are not public
- **API**: Draft posts are excluded from all public API endpoints

### Publishing a Draft

To publish a draft post:
1. Change `status: "draft"` to `status: "published"` (or remove the status field entirely)
2. Save the file
3. The post will immediately become visible to all users

## Author Management

### Creating a New Author

1. Create a JSON file in `content/authors/` with the author's slug:
   ```
   content/authors/john-doe.json
   ```

2. Add author information:
   ```json
   {
     "name": "John Doe",
     "image": "https://example.com/john-doe.jpg",
     "bio": "John is a business strategist specializing in Swadeshi enterprises.",
     "social": {
       "twitter": "@johndoe",
       "linkedin": "in/john-doe",
       "github": "johndoe"
     }
   }
   ```

3. Use the author slug in your post frontmatter:
   ```yaml
   author: "john-doe"
   ```

## Content Guidelines

### Writing Style
- Use clear, concise language
- Write in active voice
- Include relevant examples
- Break up long paragraphs
- Use headings to structure content

### SEO Best Practices
- Include target keywords naturally
- Write compelling meta descriptions
- Use descriptive headings (H2, H3)
- Add alt text to images
- Internal linking to related posts

### Image Guidelines
- Use high-quality images (minimum 1200px wide)
- Optimize file sizes for web
- Include descriptive alt text
- Use consistent aspect ratios for cover images

## Advanced Features

### Custom Components

You can use React components in your MDX content:

```jsx
import { Callout } from '@/components/ui/callout'

<Callout type="info">
This is an important note for readers.
</Callout>
```

### Code Blocks

Use fenced code blocks with language specification:

```javascript
function greetSwadeshi() {
  console.log("Support Local Businesses!");
}
```

### Tables

Create tables using Markdown syntax:

```markdown
| Product | Origin | Price |
|---------|--------|-------|
| Khadi Shirt | Gujarat | ₹1,200 |
| Wooden Toy | Karnataka | ₹800 |
```

## Publishing Workflow

### Development
1. Create your MDX file
2. Run `npm run dev` to preview locally
3. Visit `http://localhost:3001/blog/your-slug` to see your post

### Production
1. Commit your changes to Git
2. Push to your repository
3. Deploy (posts are automatically included in build)

## Common Issues & Solutions

### Post Not Appearing
- Check frontmatter syntax (YAML format)
- Ensure date is in correct format (YYYY-MM-DD)
- Verify file is in correct folder structure
- Check for syntax errors in MDX

### Images Not Loading
- Verify image URLs are accessible
- Check image file extensions
- Ensure proper aspect ratios

### Author Not Found
- Create author JSON file in `content/authors/`
- Match author slug exactly in frontmatter
- Check JSON syntax in author file

## File Naming Conventions

### Blog Posts
- Use kebab-case: `my-awesome-post.mdx`
- Keep slugs short but descriptive
- Avoid special characters and spaces

### Authors
- Use kebab-case: `john-doe.json`
- Match the slug used in post frontmatter

### Categories and Tags
- Use Title Case for categories: `"Business Strategy"`
- Use lowercase for tags: `"startup", "local-business"`

## Example Complete Post

```yaml
---
title: "Building a Sustainable Swadeshi Business in 2024"
slug: "building-sustainable-swadeshi-business-2024"
excerpt: "Learn how to create a profitable local business that supports the Swadeshi movement while building a sustainable future."
coverImage: "https://images.pexels.com/photos/3768142/pexels-photo-3768142.jpeg"
date: "2024-12-20"
author: "swadesic-team"
categories: ["Business", "Sustainability", "Swadeshi"]
tags: ["entrepreneurship", "local-business", "sustainability", "swadeshi-movement"]
readingTime: "7 min read"
seo:
  metaTitle: "Building a Sustainable Swadeshi Business in 2024 | Complete Guide"
  metaDescription: "Complete guide to starting and growing a sustainable Swadeshi business. Learn strategies, best practices, and success stories from local entrepreneurs."
  keywords: ["swadeshi business", "sustainable entrepreneurship", "local business", "indian startups"]
---

# Building a Sustainable Swadeshi Business in 2024

The Swadeshi movement is more than just buying local—it's about creating a sustainable ecosystem that benefits communities, preserves traditions, and builds economic independence.

## Why Swadeshi Matters Today

In an increasingly globalized world, supporting local businesses has become crucial for:

- **Economic Independence**: Keeping money within local communities
- **Cultural Preservation**: Maintaining traditional crafts and skills
- **Environmental Impact**: Reducing carbon footprint through local sourcing
- **Job Creation**: Providing employment in local areas

## Getting Started

### 1. Identify Your Niche
Research local needs and traditional products that have modern applications.

### 2. Build Authentic Relationships
Connect with local artisans, suppliers, and community leaders.

### 3. Focus on Quality
Swadeshi products should represent the best of Indian craftsmanship.

## Success Stories

Many entrepreneurs have built thriving businesses by focusing on authentic, local products while leveraging modern marketing and distribution channels.

**Remember**: Building a Swadeshi business is not just about profit—it's about contributing to a movement that strengthens our communities and preserves our heritage.
```

## Need Help?

If you encounter any issues or need assistance:
1. Check the existing blog posts for examples
2. Review the frontmatter syntax carefully
3. Test locally before publishing
4. Reach out to the development team for technical support
