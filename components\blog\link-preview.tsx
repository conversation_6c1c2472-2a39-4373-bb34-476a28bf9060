"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { ExternalLink } from "lucide-react"

interface LinkPreviewProps {
  url: string
}

export function LinkPreview({ url }: LinkPreviewProps) {
  const [favicon, setFavicon] = useState<string>("")
  const [hostname, setHostname] = useState<string>("")

  useEffect(() => {
    try {
      const urlObj = new URL(url)
      setHostname(urlObj.hostname)
      setFavicon(`https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=32`)
    } catch (error) {
      console.error("Invalid URL:", error)
    }
  }, [url])

  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className="flex-shrink-0 block w-72 p-4 bg-secondary rounded-lg hover:bg-secondary/80 transition-colors group"
    >
      <div className="flex items-center gap-2 mb-2">
        {favicon && (
          <Image
            src={favicon}
            alt={hostname}
            className="w-4 h-4"
            width={16}
            height={16}
          />
        )}
        <span className="text-sm font-medium truncate">{hostname}</span>
        <ExternalLink className="w-4 h-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity" />
      </div>
      <p className="text-sm text-muted-foreground line-clamp-2">{url}</p>
    </a>
  )
}

interface LinkPreviewsProps {
  links: string[]
}

export function LinkPreviews({ links }: LinkPreviewsProps) {
  if (links.length === 0) return null

  return (
    <section className="mt-16 pt-8 border-t">
      <h2 className="text-lg font-semibold mb-4">Links Mentioned</h2>
      <div className="relative">
        <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-thin scrollbar-thumb-secondary scrollbar-track-transparent">
          {links.map((link) => (
            <LinkPreview key={link} url={link} />
          ))}
        </div>
        <div className="absolute right-0 top-0 bottom-4 w-20 bg-gradient-to-l from-background to-transparent pointer-events-none" />
      </div>
    </section>
  )
}
