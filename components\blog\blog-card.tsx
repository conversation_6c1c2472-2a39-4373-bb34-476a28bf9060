"use client"

import { useState, useEffect } from "react"
import { BlogPost } from "@/types/blog"
import Image from "next/image"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface CategoryData {
  name: string
  slug: string
  description?: string
  image: string
  color?: string
}

interface BlogCardProps {
  post: BlogPost
  featured?: boolean
}

export function BlogCard({ post, featured = false }: BlogCardProps) {
  // Get the first category with its data
  const [categoryData, setCategoryData] = useState<CategoryData>({
    name: post.categories?.[0] || 'Uncategorized',
    slug: post.categories?.[0] ? post.categories[0].toLowerCase().replace(/\s+/g, '-') : 'uncategorized',
    color: '#6B7280', // Default gray color
    image: '/images/categories/default.jpg'
  })

  // Load category data on the client side
  useEffect(() => {
    const loadCategoryData = async () => {
      if (post.categories?.[0]) {
        try {
          // Dynamically import the server-side function
          const categoriesModule = await import('@/lib/categories')
          const categorySlug = post.categories[0].toLowerCase().replace(/\s+/g, '-')
          const category = await categoriesModule.getCategoryBySlug(categorySlug)
          
          if (category) {
            setCategoryData({
              name: category.name,
              slug: category.slug,
              color: category.color || '#6B7280',
              image: category.image || '/images/categories/default.jpg'
            })
          }
        } catch (error) {
          console.error('Error loading category data:', error)
          // Fallback to default category data if there's an error
          setCategoryData({
            name: post.categories[0],
            slug: post.categories[0].toLowerCase().replace(/\s+/g, '-'),
            color: '#6B7280',
            image: '/images/categories/default.jpg'
          })
        }
      }
    }
    
    loadCategoryData()
  }, [post.categories])

  return (
    <div className={cn(
      "group overflow-hidden",
      featured && "relative"
    )}>
      <div className={cn(
        "relative aspect-video overflow-hidden rounded-lg mb-4",
        "transition-all duration-300 group-hover:shadow-lg"
      )}>
        <Link href={`/blog/${post.slug}`} className="block w-full h-full">
          <Image
            src={post.coverImage}
            alt={post.title}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            sizes={featured ? "(max-width: 768px) 100vw, 50vw" : "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
        </Link>

        {/* Category badge */}
        <div className="absolute top-3 left-3 z-10">
          <Link 
            href={`/blog/category/${categoryData.slug}`}
            className={cn(
              "inline-flex items-center px-3 py-1 rounded-full text-xs font-medium",
              "transition-colors duration-200 hover:opacity-90",
              "text-white backdrop-blur-sm"
            )}
            style={{
              backgroundColor: `${categoryData.color}CC`, // Add transparency
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
            }}
          >
            {categoryData.name}
          </Link>
        </div>
      </div>

      <div className="space-y-3">
        {/* Additional categories as small pills */}
        {post.categories.length > 1 && (
          <div className="flex flex-wrap gap-2">
            {post.categories.slice(1, 3).map((category, index) => {
              const catSlug = category.toLowerCase().replace(/\s+/g, '-')
              const catData = getCategoryBySlug(catSlug) || {
                name: category,
                slug: catSlug,
                color: '#6B7280'
              }
              
              return (
                <Link
                  key={catSlug}
                  href={`/blog/category/${catSlug}`}
                  className={cn(
                    "text-xs px-2 py-0.5 rounded-full transition-colors",
                    "hover:opacity-90"
                  )}
                  style={{
                    backgroundColor: `${catData.color}1A`,
                    color: catData.color,
                    border: `1px solid ${catData.color}33`
                  }}
                >
                  {category}
                </Link>
              )
            })}
            {post.categories.length > 3 && (
              <span className="text-xs text-muted-foreground">
                +{post.categories.length - 3} more
              </span>
            )}
          </div>
        )}

        <h3 className={cn(
          "font-semibold leading-tight",
          featured ? "text-xl" : "text-lg"
        )}>
          <Link
            href={`/blog/${post.slug}`}
            className="hover:text-primary transition-colors line-clamp-2"
          >
            {post.title}
          </Link>
        </h3>

        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>by {post.author.name}</span>
          <span className="opacity-50">•</span>
          <time dateTime={post.date}>
            {new Date(post.date).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
              year: "numeric",
            })}
          </time>
          <span className="opacity-50">•</span>
          <span>{Math.ceil(Number(post.readingTime) || 5)} min read</span>
        </div>
      </div>
    </div>
  )
}
