"use client"

import { BlogPost } from "@/types/blog"
import Image from "next/image"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface BlogCardProps {
  post: BlogPost
  featured?: boolean
}

export function BlogCard({ post, featured = false }: BlogCardProps) {
  return (
    <div className="group overflow-hidden">
      <Link href={`/blog/${post.slug}`} className="block relative aspect-video overflow-hidden rounded-lg mb-4">
        <Image
          src={post.coverImage}
          alt={post.title}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
        />
      </Link>

      <div className="space-y-2">
        <div className="flex flex-wrap gap-2">
          {post.categories.slice(0, 1).map((category) => (
            <Link
              key={category}
              href={`/blog/category/${category.toLowerCase()}`}
              className="text-xs px-3 py-1 rounded-full bg-secondary hover:bg-secondary/80 transition-colors"
            >
              {category}
            </Link>
          ))}
        </div>

        <h3 className={cn(featured ? "text-2xl" : "text-xl", "font-bold")}>
          <Link
            href={`/blog/${post.slug}`}
            className="hover:text-primary transition-colors line-clamp-2"
          >
            {post.title}
          </Link>
        </h3>

        <div className="flex items-center text-sm text-muted-foreground">
          <span>by {post.author.name}</span>
          <span className="mx-1">•</span>
          <time dateTime={post.date}>
            {new Date(post.date).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
              year: "numeric",
            })}
          </time>
        </div>
      </div>
    </div>
  )
}
