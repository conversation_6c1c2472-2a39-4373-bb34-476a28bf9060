import { Store, MessageSquare, Share2, ClipboardList, Users, BarChart3, Wallet, Shield, LineChart, Package, FileText, Bell, Zap, CreditCard, Truck, Lock } from "lucide-react"
import { Button } from "@/components/ui/button"

export default function BenefitsPage() {
  const benefits = [
    {
      title: "Store & Product Management",
      icon: Store,
      description: "Everything you need to manage your store effectively",
      features: [
        {
          title: "Unlimited Products",
          description: "Showcase your entire inventory without limits. This gives you the freedom to offer a wide range of products, attracting more customers and increasing sales potential.",
          icon: Package
        },
        {
          title: "Easy Product Catalogue",
          description: "Organize your products effectively with detailed descriptions and high-quality images, helping customers make informed purchasing decisions and improving your store's credibility.",
          icon: FileText
        },
        {
          title: "Custom Superlink",
          description: "Get a unique link for your store, making it easier for you to share and promote across social media. Drive traffic and grow your audience without extra effort.",
          icon: Share2
        },
        {
          title: "Efficient Order Management",
          description: "From payment to delivery, everything is automated, including cancellations, returns, and refunds. Reducing operational overhead and avoiding errors - means more time for you to focus on growing your business.",
          icon: ClipboardList
        }
      ]
    },
    {
      title: "Community Building & Customer Engagement",
      icon: Users,
      description: "Build loyalty and lasting relationships with your customers",
      features: [
       
        {
          title: "Build a Community",
          description: "Swadesic isn't just a marketplace—it's a community. Engage customers in a meaningful way by creating a space for interaction, discussions, and sharing feedback.",
          icon: Users
        },
        {
          title: "In-App Messaging",
          description: "Engage directly with your customers, answer queries instantly, and resolve issues quickly, ensuring a superior customer experience and build loyalty, which can lead to repeat purchases and positive reviews.",
          icon: MessageSquare
        },
        {
          title: "Product Comments & Reviews",
          description: "Displaying customer feedback helps to build social proof and trust. Positive comments and reviews boost your credibility, which can lead to more sales and returning customers.",
          icon: MessageSquare
        }
      ]
    },
    {
      title: "Marketing & Growth",
      icon: BarChart3,
      description: "Tools to help you grow your business",
      features: [
        {
          title: "Business & Branding Analytics",
          description: "Gain valuable insights into your store's performance. Understanding key metrics lets you make data-driven decisions, optimize product offerings, Content and track growth opportunities.",
          icon: LineChart
        },
        {
          title: "Flash Points",
          description: "Save on fees and unlock additional benefits by using earned points. These rewards system incentivizes growth while making it easier for you to reinvest in your business.",
          icon: Zap
        }
      ]
    },
    {
      title: "Reliable Payment & Settlements",
      icon: Wallet,
      description: "Secure and efficient payment processing",
      features: [
        {
          title: "Pay Per Sale Model",
          description: "No hidden costs, no upfront payments. You pay a small fee only when you make a successful sale, allowing you to grow your business without financial risk.",
          icon: CreditCard
        },
        {
          title: "No Fees for Cancellations, Returns & Orders Below ₹500",
          description: "Eliminate unnecessary costs with no fees for cancellations or returns, and no platform fee for orders below ₹500. This ensures you retain more of your revenue.",
          icon: Wallet
        },
        {
          title: "Secure Payments",
          description: "Your business and customer transactions are protected through secure payment processing - avoiding any fraud involved.",
          icon: Lock
        },
        {
          title: "Fast Payouts",
          description: "Get your earnings quickly with T+1 day payouts after fulfillment. This ensures you can keep your operations smooth and pay suppliers or reinvest in your business without delays.",
          icon: Zap
        }
      ]
    },
    {
      title: "Trust & Security",
      icon: Shield,
      description: "Keep your business and customers protected",
      features: [
        {
          title: "Tracking for You and Your Customers",
          description: "Keep everyone informed every step of the way. With order tracking, your customers stay updated, which reduces inquiries and increases satisfaction.",
          icon: Truck
        },
        {
          title: "Secure Payments",
          description: "Payments are securely processed through Razorpay, providing a reliable and trusted solution for handling transactions, so you can focus on growing your business.",
          icon: CreditCard
        },
        {
          title: "Unified Notifications",
          description: "Never miss an important update. Receive notifications on both your seller and buyer account so you're always in the loop, regardless of what part of the platform you're using.",
          icon: Bell
        }
      ]
    },
    {
      title: "Scalability for Growing Businesses",
      icon: LineChart,
      description: "Grow without limits",
      features: [
        {
          title: "Unlimited Orders",
          description: "As your business expands, you won't be limited by order restrictions. This allows you to scale your operations without worrying about platform constraints, giving you the flexibility to grow at your own pace.",
          icon: Package
        },
        {
          title: "Advanced Marketing & Analytics",
          description: "Access deeper insights and more powerful marketing features as your business matures. This lets you make strategic decisions that can significantly increase your sales and customer engagement.",
          icon: BarChart3
        }
      ]
    }
  ]

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="container pt-32 py-8 md:py-12 lg:py-24">
          <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
            <h1 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
              Designed to Grow Your Business
            </h1>
            <p className="max-w-[85%] leading-normal text-black sm:text-lg sm:leading-7">
              Swadesic&apos;s innovative platform is designed to help India&apos;s small businesses thrive in the digital age. Whether you&apos;re just starting out or scaling, here&apos;s everything you get, regardless of which plan you choose.
            </p>
          </div>
        </section>

        {/* Benefits Sections */}
        {benefits.map((category, index) => (
          <section 
            key={category.title}
            className={`w-full ${index % 2 === 0 ? 'bg-white' : 'bg-gray-100'}`}
          >
            <div className="container py-24">
              <div className="mx-auto max-w-5xl">
                {/* Category Header */}
                <div className="flex flex-col items-start gap-4 mb-12">
                  <div className="w-10 h-10 md:w-14 md:h-14 lg:w-16 lg:h-16 flex items-center justify-center">
                    <category.icon className="h-7 w-7 md:h-10 md:w-10 lg:h-12 lg:w-12 text-[var(--brand-color)]" />
                  </div>
                  <h2 className="text-2xl font-bold text-black">{category.title}</h2>
                  <p className="text-black">{category.description}</p>
                </div>

                {/* Features Grid */}
                <div className="grid gap-8 md:grid-cols-2">
                  {category.features.map((feature) => (
                    <div key={feature.title} className="flex gap-4">
                      <div className="w-8 h-8 flex items-center justify-center">
                        <feature.icon className="h-6 w-6 flex-shrink-0 text-[var(--brand-color)]" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2 text-black">{feature.title}</h3>
                        <p className="text-black">{feature.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>
        ))}

        {/* Download CTA Section */}
        <section className="w-full bg-[var(--brand-color)]">
          <div className="container py-24">
            <div className="mx-auto max-w-5xl text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-black mb-6">
                Download the App and Start Selling for Free
              </h2>
              <p className="text-black text-lg mb-8 max-w-2xl mx-auto">
                Join thousands of local businesses growing with Swadesic. No upfront costs, no hidden fees - just pure growth potential.
              </p>
              <Button 
                size="lg"
                className="bg-black text-white hover:bg-black/90 font-semibold text-lg px-8"
                asChild
              >
                <a 
                  href="https://play.google.com/store/apps/details?id=com.sociallyx.swadesic" 
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  Download Now
                </a>
              </Button>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}
