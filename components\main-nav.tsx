import Link from "next/link"
import { NavigationMenu, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuContent, NavigationMenuTrigger, navigationMenuTriggerStyle } from "@/components/ui/navigation-menu"
import { cn } from "@/lib/utils"
import { mainNav } from "@/config/navigation"

export function MainNav() {
  return (
    <NavigationMenu className="max-w-full">
      <NavigationMenuList className="gap-2">
        {mainNav.map((item) => (
          <NavigationMenuItem key={item.title}>
            {item.href ? (
              <Link href={item.href} legacyBehavior passHref>
                <NavigationMenuLink className={cn(
                  navigationMenuTriggerStyle(),
                  "text-sm font-medium text-muted-foreground hover:text-foreground"
                )}>
                  {item.title}
                </NavigationMenuLink>
              </Link>
            ) : (
              <>
                <NavigationMenuTrigger className="text-sm font-medium text-muted-foreground hover:text-foreground">
                  {item.title}
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <div className="fixed left-0 right-0 top-full w-screen bg-white shadow-lg border-b z-50">
                    <div className="mx-auto w-full max-w-7xl">
                      <div className="grid grid-cols-3 gap-x-8 gap-y-10 p-8">
                        {item.categories?.map((category) => (
                          <div key={category.title}>
                            <h3 className="text-sm font-semibold text-muted-foreground mb-4">
                              {category.title}
                            </h3>
                            <div className="grid gap-6">
                              {category.items.map((subItem) => (
                                <Link
                                  key={subItem.title}
                                  href={subItem.href || "#"}
                                  {...(subItem.external ? { target: "_blank", rel: "noopener noreferrer" } : {})}
                                  className="group"
                                >
                                  <div className="text-base font-medium text-foreground group-hover:text-primary">
                                    {subItem.title}
                                  </div>
                                  {subItem.description && (
                                    <p className="mt-1 text-sm text-muted-foreground group-hover:text-foreground/80">
                                      {subItem.description}
                                    </p>
                                  )}
                                </Link>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </NavigationMenuContent>
              </>
            )}
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  )
}
