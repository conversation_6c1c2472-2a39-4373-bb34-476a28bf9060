import { BlogPost } from "@/types/blog"

export function getRelatedPosts(currentPost: BlogPost, allPosts: BlogPost[], limit: number = 3): BlogPost[] {
  // Remove current post from the pool and filter only published posts
  const otherPosts = allPosts.filter((post) =>
    post.id !== currentPost.id && post.status === 'published'
  )

  // Calculate relevance score for each post
  const scoredPosts = otherPosts.map((post) => {
    let score = 0

    // Score based on shared categories (highest weight)
    const sharedCategories = post.categories.filter((category) =>
      currentPost.categories.includes(category)
    )
    score += sharedCategories.length * 3

    // Score based on shared tags (medium weight)
    const sharedTags = post.tags.filter((tag) => currentPost.tags.includes(tag))
    score += sharedTags.length * 2

    // Score based on same author (low weight)
    if (post.author.name === currentPost.author.name) {
      score += 1
    }

    // Boost score for recent posts (within last 30 days)
    const postDate = new Date(post.date)
    const currentDate = new Date()
    const daysDiff = Math.abs((currentDate.getTime() - postDate.getTime()) / (1000 * 60 * 60 * 24))
    if (daysDiff <= 30) {
      score += 0.5
    }

    // Add small randomness to avoid showing the same related posts every time
    score += Math.random() * 0.3

    return { post, score }
  })

  // Sort by score (highest first) and return top N posts
  const sortedPosts = scoredPosts
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map((item) => item.post)

  // If we don't have enough related posts with good scores,
  // fill with most recent posts
  if (sortedPosts.length < limit) {
    const recentPosts = otherPosts
      .filter(post => !sortedPosts.some(sp => sp.id === post.id))
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limit - sortedPosts.length)

    sortedPosts.push(...recentPosts)
  }

  return sortedPosts
}
