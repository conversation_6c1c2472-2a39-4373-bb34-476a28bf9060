import { Suspense } from 'react'
import Loading from './loading'

export const dynamic = 'force-dynamic'

// Move the original page content to a separate component
async function AuthorsContent() {
  const { default: dynamicContent } = await import('./authors-content')
  return dynamicContent()
}

export default function AuthorsPage() {
  return (
    <Suspense fallback={<Loading />}>
      <AuthorsContent />
    </Suspense>
  )
}


export const metadata: Metadata = {
  title: "Authors | Swadesic Blog",
  description: "Meet the writers and contributors behind the Swadesic blog. Learn about their expertise and read their latest articles.",
  openGraph: {
    title: "Authors | Swadesic Blog",
    description: "Meet the writers and contributors behind the Swadesic blog.",
  },
}

interface AuthorWithStats {
  slug: string
  name: string
  image: string
  bio?: string
  social?: {
    twitter?: string
    linkedin?: string
    github?: string
  }
  postCount: number
}

async function getAllAuthors(): Promise<AuthorWithStats[]> {
  const posts = await getAllBlogPosts()
  const authorSlugs = new Set<string>()
  const authorPostCounts = new Map<string, number>()
  
  // Get unique authors and count their posts
  posts.forEach((post: BlogPost) => {
    if (!post.author) return;
    const authorSlug = post.author.name.toLowerCase().replace(/\s+/g, '-')
    authorSlugs.add(authorSlug)
    authorPostCounts.set(authorSlug, (authorPostCounts.get(authorSlug) || 0) + 1)
  })
  
  // Get author data for each author
  const authors: AuthorWithStats[] = []
  
  // Convert Set to Array before iteration
  for (const slug of Array.from(authorSlugs)) {
    if (!slug) continue;
    
    let authorData;
    try {
      authorData = getAuthorData(slug);
      if (!authorData) {
        console.warn(`No author data found for slug: ${slug}`);
        continue;
      }
      authors.push({
        ...authorData,
        slug,
        postCount: authorPostCounts.get(slug) || 0,
      });
    } catch (error) {
      console.error(`Error loading author data for ${slug}:`, error);
    }
  }
  
  // Sort by post count (most prolific first)
  return authors.sort((a, b) => b.postCount - a.postCount)
}

export default async function AuthorsPage() {
  const authors = await getAllAuthors()

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="max-w-4xl mx-auto mb-12 text-center">
          <h1 className="text-4xl font-bold mb-4 text-foreground">
            Our Authors
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Meet the talented writers and experts who contribute to the Swadesic blog. 
            From business strategists to technology advocates, our authors bring diverse 
            perspectives on the Swadeshi movement and local entrepreneurship.
          </p>
        </div>

        {/* Authors Grid */}
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {authors.map((author) => (
              <div
                key={author.slug}
                className="bg-card border border-border rounded-lg p-6 hover:shadow-lg transition-shadow"
              >
                {/* Author Image */}
                <div className="w-20 h-20 rounded-full overflow-hidden mx-auto mb-4">
                  <Image
                    src={author.image}
                    alt={author.name}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Author Info */}
                <div className="text-center">
                  <h3 className="text-xl font-bold mb-2 text-foreground">
                    <Link
                      href={`/blog/author/${author.slug}`}
                      className="hover:text-primary transition-colors"
                    >
                      {author.name}
                    </Link>
                  </h3>
                  
                  {author.bio && (
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                      {author.bio}
                    </p>
                  )}
                  
                  {/* Post Count */}
                  <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mb-4">
                    <User className="w-4 h-4" />
                    <span>
                      {author.postCount} {author.postCount === 1 ? 'article' : 'articles'}
                    </span>
                  </div>
                  
                  {/* Social Links */}
                  {author.social && (
                    <div className="flex justify-center gap-3 mb-4">
                      {author.social.twitter && (
                        <a
                          href={`https://twitter.com/${author.social.twitter.replace('@', '')}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-muted-foreground hover:text-primary transition-colors"
                          aria-label={`${author.name} on Twitter`}
                        >
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      )}
                      {author.social.linkedin && (
                        <a
                          href={`https://linkedin.com/${author.social.linkedin}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-muted-foreground hover:text-primary transition-colors"
                          aria-label={`${author.name} on LinkedIn`}
                        >
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      )}
                      {author.social.github && (
                        <a
                          href={`https://github.com/${author.social.github}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-muted-foreground hover:text-primary transition-colors"
                          aria-label={`${author.name} on GitHub`}
                        >
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      )}
                    </div>
                  )}
                  
                  {/* View Articles Button */}
                  <Link
                    href={`/blog/author/${author.slug}`}
                    className="inline-block bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90 transition-colors"
                  >
                    View Articles
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Back to Blog */}
        <div className="max-w-4xl mx-auto mt-12 text-center">
          <Link
            href="/blog"
            className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
          >
            ← Back to all articles
          </Link>
        </div>
      </div>
    </div>
  )
}
