import Link from "next/link"
import Image from "next/image"
import { mainNav } from "@/config/navigation"
import { Instagram, Twitter, Youtube } from "lucide-react"

export function Footer() {
  return (
    <footer className="bg-black text-white">
      <div className="mx-auto max-w-7xl px-6 py-12 md:py-18 lg:px-8 lg:py-24">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          {/* Brand Section */}
          <div className="space-y-8">
            <Link href="/" className="flex items-center space-x-2">
              <Image 
                src="/logos/swadesic-logo-transparent-smaller-5x.png" 
                alt="Swadesic Logo" 
                width={32} 
                height={32} 
                className="w-8 h-8"
              />
              <span className="text-2xl font-bold text-white">Swadesic</span>
            </Link>
            <p className="text-sm text-gray-300 max-w-xs">
              Swadesic is an open marketplace and social network where sellers can create free stores and build communities, receiving orders directly from engaged followers. With story-driven product catalogs that deepen connections, helping Swadeshi brands secure long-term loyalty through every sale in a simple, flat fee pay-per-sale model.
            </p>
            <div className="flex space-x-4">
              <Link 
                href="https://instagram.com/swadesic" 
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <span className="sr-only">Instagram</span>
                <Instagram className="h-5 w-5" />
              </Link>
              <Link 
                href="https://x.com/SwadesicApp" 
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <span className="sr-only">Twitter</span>
                <Twitter className="h-5 w-5" />
              </Link>
              <Link 
                href="https://www.youtube.com/@swadesic" 
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <span className="sr-only">YouTube</span>
                <Youtube className="h-5 w-5" />
              </Link>
              <Link 
                href="/policies" 
                className="text-base text-gray-400 hover:text-white transition-colors"
              >
                Policies
              </Link>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              {mainNav.slice(0, 2).map((section) => (
                <div key={section.title} className="mt-10 md:mt-0">
                  <h3 className="text-sm font-semibold text-white">{section.title}</h3>
                  <div className="mt-6 space-y-12">
                    {section.categories?.map((category) => (
                      <div key={category.title}>
                        <h4 className="text-sm font-medium text-gray-400 mb-4">{category.title}</h4>
                        <ul role="list" className="space-y-4">
                          {category.items.map((item) => (
                            <li key={item.title}>
                              <Link
                                href={item.href || "#"}
                                className="text-sm text-gray-300 hover:text-white transition-colors"
                                {...(item.external ? { target: "_blank", rel: "noopener noreferrer" } : {})}
                              >
                                {item.title}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
            <div className="md:grid md:grid-cols-1 md:gap-8">
              {mainNav.slice(2).map((section) => (
                <div key={section.title} className="mt-10 md:mt-0">
                  <h3 className="text-sm font-semibold text-white">{section.title}</h3>
                  <div className="mt-6 space-y-12">
                    {section.categories?.map((category) => (
                      <div key={category.title}>
                        <h4 className="text-sm font-medium text-gray-400 mb-4">{category.title}</h4>
                        <ul role="list" className="space-y-4">
                          {category.items.map((item) => (
                            <li key={item.title}>
                              <Link
                                href={item.href || "#"}
                                className="text-sm text-gray-300 hover:text-white transition-colors"
                                {...(item.external ? { target: "_blank", rel: "noopener noreferrer" } : {})}
                              >
                                {item.title}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-16 border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-xs text-gray-400">
              &copy; 2024 Swadesic by Socially X Influencer Centric Pvt Ltd. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <Link 
                href="/privacy" 
                className="text-xs text-gray-400 hover:text-white transition-colors"
              >
                Privacy Policy
              </Link>
              <Link 
                href="/terms" 
                className="text-xs text-gray-400 hover:text-white transition-colors"
              >
                Terms of Service
              </Link>
              <Link 
                href="/cookies" 
                className="text-xs text-gray-400 hover:text-white transition-colors"
              >
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
