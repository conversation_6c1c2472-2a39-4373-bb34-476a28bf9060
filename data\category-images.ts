// Category images mapping
// Place images in public/images/categories/{slug}.{jpg|png|webp}

export const categoryImages: Record<string, string> = {
  // E-commerce
  'ecommerce': '/images/categories/ecommerce.jpg',
  
  // Small Business
  'small-business': '/images/categories/small-business.jpg',
  
  // Add more categories and their corresponding images here
  // Format: 'category-slug': '/path/to/image.jpg'
}

// Fallback image URL
export const DEFAULT_CATEGORY_IMAGE = '/images/categories/default.jpg'
