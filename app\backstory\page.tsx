import { Container } from "@/components/ui/container"

export default function BackstoryPage() {
  return (
    <Container as="main" className="py-12 sm:py-16">
      <article className="prose prose-lg dark:prose-invert mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center">What drives Swadesic?</h1>

        <div className="text-lg text-muted-foreground mb-12">
          Hello, I&apos;m <PERSON><PERSON><PERSON>, happy to have you here and be interested in our founding story. 
          Even though it is coming from me, it is a story of every child in our nation that is brought up by a Small business.
          I am from Nellore, Andhra Pradesh.
        </div>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">The Beginning</h2>
          <p>
            My grandfather, a man of simple means, moved to Nellore in the 1950s and started a small sweet production. 
            Life was tough back then. My father, unable to continue school beyond the 2nd grade, started working at 
            local bakeries when he was just a little boy of age 7. Despite the hardships, he was full of dreams and determination.
          </p>
          <p>
            By the time he was 21, he used a small government loan to open his own bakery in 1986. It was a hit, 
            earning about ₹5000 a week, a big deal at that time! He&apos;s my Hero and an inspiration throughout my life.
          </p>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Growing Up in the Bakery</h2>
          <p>
            For over 30 years, our bakery was the heart of our family, helping us through thick and thin. I grew up 
            in that bakery, coming home from school to the smell of fresh biscuits, helping my dad with cake orders, 
            and even sleeping there when work went late into the night.
          </p>
          <p>
            We had our tough times, sure, but it taught me how precious a small business is. It&apos;s not just about 
            the person who runs it, but about all the lives it touches. That bakery helped raise four kids, turning 
            us into capable professionals. That&apos;s why I hold small businesses close to my heart; I&apos;ve seen the 
            difference they can make.
          </p>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">The Fire Within</h2>
          <p>
            Growing up, I was always curious, I would spend hours reading about different ideas, like philosophy 
            and martial arts, and more. The struggles of my family made me ambitious, wanting to reach beyond the 
            ordinary. Unlike my friends who were planning their careers, I was unprepared but just ambitious, 
            believing I could do anything I set my mind to.
          </p>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">A Turn in the Road</h2>
          <p>
            Life had other plans. I had to put my dreams on hold and get a job to support my family. Moving to 
            Hyderabad, I thought I could juggle a job and my network marketing dreams, but soon realized creating 
            value was the real path to prosperity. After a brief break in a job, I dove into data science, and it 
            was here I found my footing again. But the entrepreneurial spark never died. I dabbled in various ideas, 
            from nutritious foods to AI, but nothing stuck until I noticed the struggles of small businesses in the 
            digital world.
          </p>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Swadesic: The Dream Takes Shape</h2>
          <p>
            It hit me while working on the Influencer project (back then Socially X). Small businesses lacked a 
            platform that truly understood their needs. They needed more than just marketing; they needed a space 
            to grow, to connect with their customers, and to build a loyal community. That&apos;s when Swadesic started 
            to take shape. I put aside other projects, choosing to focus on this vision that could make a real difference.
          </p>
          <p>
            With the support of friends and new team members, we began building Swadesic. It was a tough journey, 
            balancing a demanding job and managing a growing team, but our passion kept us going.
          </p>
        </section>

        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Swadesic Comes to Life</h2>
          <p>
            Two years of hard work later, Swadesic was ready. Our platform wasn&apos;t just about selling products; 
            it was about bringing people together, celebrating the diversity of Indian commerce, and empowering 
            local businesses. Swadesic is more than a marketplace; it&apos;s a movement to bring back India&apos;s glory 
            as a leading economy, just like it was centuries ago.
          </p>
          <p>
            We&apos;re here to support small businesses, to help them grow and thrive. It&apos;s a tribute to my father&apos;s 
            bakery and every small business that holds a community together. Join us in this journey to make a difference.
          </p>
        </section>

        <div className="text-center text-2xl font-bold text-primary mt-16">
          Jai Hind!
        </div>
      </article>
    </Container>
  )
}
