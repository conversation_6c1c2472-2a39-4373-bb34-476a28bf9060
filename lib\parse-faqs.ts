export interface FAQ {
  question: string
  answer: string
  category?: string
}

export function parseFAQs(markdown: string): FAQ[] {
  const faqs: FAQ[] = []
  const lines = markdown.split('\n')
  
  let currentCategory = ''
  let currentQuestion = ''
  let currentAnswer = ''
  let inTable = false
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    
    if (line.startsWith('# ')) {
      // This is a category
      currentCategory = line.substring(2).trim()
      // Don't reset question/answer as they might be in progress
    } else if (line.startsWith('## ')) {
      // If we have a previous QA pair, save it
      if (currentQuestion && currentAnswer) {
        faqs.push({
          question: currentQuestion,
          answer: currentAnswer.trim(),
          category: currentCategory
        })
      }
      // Start new question, remove the ## and trim
      currentQuestion = line.substring(3).trim()
      currentAnswer = ''
      inTable = false
    } else if (line && currentQuestion) {
      // Check if this is a table row
      if (line.startsWith('|') || line.match(/^\s*\|/)) {
        inTable = true
        currentAnswer += (currentAnswer ? '\n' : '') + line
      }
      // If this is a separator row in a table
      else if (line.match(/^\s*[-|]+\s*$/)) {
        currentAnswer += (currentAnswer ? '\n' : '') + line
      }
      // If this line starts with a number and a dot, it's probably a list item
      else if (/^\d+\./.test(line)) {
        currentAnswer += (currentAnswer ? '\n' : '') + line
      }
      // If this line starts with a dash or asterisk, it's probably a bullet point
      else if (/^[-*•]/.test(line)) {
        currentAnswer += (currentAnswer ? '\n' : '') + line
      }
      else {
        // Regular text - preserve newlines and add proper spacing
        // If we were in a table and this is not a table row, add an extra newline
        if (inTable) {
          currentAnswer += '\n\n' + line
          inTable = false
        } else {
          currentAnswer += (currentAnswer ? '\n' : '') + line
        }
      }
    }
  }
  
  // Don't forget to add the last QA pair
  if (currentQuestion && currentAnswer) {
    faqs.push({
      question: currentQuestion,
      answer: currentAnswer.trim(),
      category: currentCategory
    })
  }
  
  return faqs
}
