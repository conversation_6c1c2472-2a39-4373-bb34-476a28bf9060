"use client"

import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"

interface Feature {
  title: string
  description: string
  image: string
}

interface FeaturesSectionAdaptiveProps {
  className?: string
  features: Feature[]
  title: string
  description: string
}

export default function FeaturesSectionAdaptive({
  className,
  features,
  title,
  description
}: FeaturesSectionAdaptiveProps) {
  return (
    <section className={`w-full py-12 md:py-24 ${className}`}>
      <div className="container px-4 md:px-6">
        <div className="text-center max-w-[800px] mx-auto mb-12">
          <h2 className="text-3xl font-medium tracking-tight md:text-5xl mb-4">
            {title}
          </h2>
          <p className="text-base text-muted-foreground">
            {description}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-16 md:gap-x-8 md:gap-y-16">
          {features.map((feature, index) => (
            <Card key={index} className="overflow-hidden border-none shadow-none bg-transparent">
              <div className="relative w-full h-full">
                <div className="md:aspect-[4/3] overflow-hidden rounded-2xl bg-gray-100">
                  <Image
                    src={feature.image}
                    alt={feature.title}
                    width={800}
                    height={600}
                    className="w-full h-auto md:h-full md:object-contain"
                  />
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
