import { getPostsByCategory } from "@/lib/mdx"
import { getCategoryBySlug, getCategoriesWithPostCounts } from "@/lib/categories"
import { InfiniteScroll } from "@/components/blog/infinite-scroll"
import { Container } from "@/components/ui/container"
import { notFound } from "next/navigation"
import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { cn } from "@/lib/utils"

interface CategoryPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const category = await getCategoryBySlug(params.slug)

  if (!category) {
    return {
      title: "Category Not Found | Swadesic Blog",
      description: "The requested category could not be found.",
    }
  }

  return {
    title: `${category.name} | Swadesic Blog`,
    description: category.description || `Explore articles about ${category.name.toLowerCase()} on the Swadesic blog.`,
    openGraph: {
      title: `${category.name} | Swadesic Blog`,
      description: category.description || `Explore articles about ${category.name.toLowerCase()} on the Swadesic blog.`,
      images: [
        {
          url: category.image,
          width: 1200,
          height: 630,
          alt: category.name,
        },
      ],
    },
  }
}

export async function generateStaticParams() {
  const categories = await getCategoriesWithPostCounts()

  return categories.map((category) => ({
    slug: category.slug,
  }))
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const category = await getCategoryBySlug(params.slug)

  if (!category) {
    notFound()
  }

  const categoryPosts = await getPostsByCategory(params.slug)
  const initialPosts = categoryPosts.slice(0, 12) // First page
  
  // If no posts found, show a message
  const hasPosts = initialPosts.length > 0

  return (
    <Container as="main" className="py-8">
      <div className="mb-8">
        <Link 
          href="/blog/category" 
          className={cn(
            "inline-flex items-center text-sm text-muted-foreground hover:text-foreground",
            "mb-6 transition-colors"
          )}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to all categories
        </Link>
        
        <div className="relative h-64 w-full mb-6 rounded-xl overflow-hidden">
          <Image
            src={category.image}
            alt={category.name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, 80vw"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          <div className="relative h-full flex flex-col justify-end p-8 text-white">
            <h1 className="text-4xl font-bold mb-2">{category.name}</h1>
            <p className="text-lg text-white/90 max-w-3xl">
              {category.description}
            </p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <p className="text-muted-foreground">
            {hasPosts 
              ? `${categoryPosts.length} article${categoryPosts.length === 1 ? "" : "s"} in this category`
              : "No articles found in this category"
            }
          </p>
          
          {!hasPosts && (
            <Link 
              href="/blog" 
              className="text-sm text-primary hover:underline inline-flex items-center"
            >
              Browse all articles
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
                className="ml-1"
              >
                <path d="M5 12h14"/>
                <path d="m12 5 7 7-7 7"/>
              </svg>
            </Link>
          )}
        </div>

      {hasPosts ? (
        <InfiniteScroll
          initialPosts={initialPosts}
          category={params.slug}
          className="mt-8"
        />
      ) : (
        <div className="text-center py-12">
          <h3 className="text-xl font-medium mb-4">No posts found in this category</h3>
          <p className="text-muted-foreground mb-6">
            We couldn't find any posts in this category. Check back later for new content.
          </p>
          <Link
            href="/blog"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Browse all articles
          </Link>
        </div>
      )}
    </div>
    </Container>
  )
}
