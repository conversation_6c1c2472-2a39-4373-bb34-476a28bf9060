import { getAllCategories, getPostsByCategory } from "@/lib/mdx"
import { InfiniteScroll } from "@/components/blog/infinite-scroll"
import { Container } from "@/components/ui/container"
import { notFound } from "next/navigation"
import { Metadata } from "next"

interface CategoryPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const categories = await getAllCategories()
  const category = categories.find((cat) => cat.slug === params.slug)

  if (!category) {
    return {
      title: "Category Not Found | Swadesic Blog",
      description: "The requested category could not be found.",
    }
  }

  return {
    title: `${category.name} | Swadesic Blog`,
    description: `Explore articles about ${category.name.toLowerCase()} on the Swadesic blog.`,
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const categories = await getAllCategories()
  const category = categories.find((cat) => cat.slug === params.slug)

  if (!category) {
    notFound()
  }

  const categoryPosts = await getPostsByCategory(params.slug)
  const initialPosts = categoryPosts.slice(0, 12) // First page

  return (
    <Container as="main" className="py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{category.name}</h1>
        <p className="text-muted-foreground">
          Showing {categoryPosts.length} article{categoryPosts.length === 1 ? "" : "s"}
        </p>
      </div>

      <InfiniteScroll
        initialPosts={initialPosts}
        category={params.slug}
      />
    </Container>
  )
}
