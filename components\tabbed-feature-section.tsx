"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"

interface TabbedFeatureSectionProps {
  features: Record<string, {
    title: string
    description: string
    image: string
  }>
  defaultTab?: string
  footerLink?: React.ReactNode
}

export default function TabbedFeatureSection({
  features,
  defaultTab = Object.keys(features)[0],
  footerLink
}: TabbedFeatureSectionProps) {
  const [activeTab, setActiveTab] = useState(defaultTab)

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-gray-50">
      <div className="container px-4 md:px-6">
        <Tabs defaultValue={defaultTab} className="w-full">
          <div className="relative">
            <TabsList className="flex w-full overflow-x-auto mb-8 bg-transparent gap-3 py-1.5 px-1 snap-x snap-mandatory min-h-[48px] items-center no-scrollbar">
              {Object.entries(features).map(([key]) => (
                <TabsTrigger 
                  key={key}
                  className="rounded-full data-[state=active]:bg-black data-[state=active]:text-white shrink-0 px-6 py-2.5 text-sm snap-start whitespace-nowrap" 
                  value={key}
                  onClick={() => setActiveTab(key)}
                >
                  {key}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div className="space-y-6">
              <TabsContent 
                value={activeTab} 
                className="mt-0"
              >
                <div>
                  <h3 className="text-2xl font-semibold mb-4">
                    {features[activeTab].title}
                  </h3>
                  <p className="text-muted-foreground">
                    {features[activeTab].description}
                  </p>
                </div>
              </TabsContent>
              {footerLink && (
                <div className="mt-6">
                  {footerLink}
                </div>
              )}
            </div>

            <div className="relative aspect-video">
              <Image 
                src={features[activeTab].image}
                alt={features[activeTab].title}
                fill
                className="rounded-lg object-cover"
              />
            </div>
          </div>
        </Tabs>
      </div>
    </section>
  )
}