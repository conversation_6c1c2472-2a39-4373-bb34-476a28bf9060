import { blogPosts } from "@/data/blog-data"
import { Calendar, Clock } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { MarkdownContent } from "@/components/blog/markdown-content"
import { TableOfContents } from "@/components/blog/table-of-contents"
import { RelatedPosts } from "@/components/blog/related-posts"
import { ShareButtons } from "@/components/blog/share-buttons"
import { LinkPreviews } from "@/components/blog/link-preview"
import { getRelatedPosts } from "@/lib/related-posts"
import { calculateReadingTime } from "@/lib/reading-time"
import { extractLinks } from "@/lib/extract-links"
import { Metadata } from "next"

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = blogPosts.find((post) => post.slug === params.slug)

  if (!post) {
    return {
      title: "Post Not Found | Swadesic Blog",
      description: "The requested blog post could not be found.",
    }
  }

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://swadesic.com'
  const postUrl = `${siteUrl}/blog/${post.slug}`

  return {
    title: `${post.title} | Swadesic Blog`,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: "article",
      publishedTime: post.date,
      authors: [post.author.name],
      images: [
        {
          url: post.coverImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
      url: postUrl,
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: [post.coverImage],
    },
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const post = blogPosts.find((post) => post.slug === params.slug)

  if (!post) {
    notFound()
  }

  const relatedPosts = getRelatedPosts(post, blogPosts)
  const readingTime = calculateReadingTime(post.content)

  return (
    <article className="container max-w-7xl py-12">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-8 lg:col-start-3">
          {/* Header */}
          <header className="space-y-4 mb-8">
            <h1 className="text-3xl font-bold">{post.title}</h1>
            <div className="flex items-center gap-x-4 text-muted-foreground text-sm">
              <div className="flex items-center gap-x-2">
                <Image
                  src={post.author.image}
                  alt={post.author.name}
                  width={28}
                  height={28}
                  className="rounded-full"
                />
                <span>{post.author.name}</span>
              </div>
              <div className="flex items-center gap-x-1">
                <Calendar className="h-4 w-4" />
                <time dateTime={post.date}>
                  {new Date(post.date).toLocaleDateString("en-US", {
                    month: "long",
                    day: "numeric",
                    year: "numeric",
                  })}
                </time>
              </div>
              <div className="flex items-center gap-x-1">
                <Clock className="h-4 w-4" />
                <span>{readingTime}</span>
              </div>
            </div>
          </header>

          {/* Cover Image */}
          <div className="relative aspect-[2/1] mb-8 overflow-hidden rounded-lg">
            <Image
              src={post.coverImage}
              alt={post.title}
              fill
              className="object-cover"
              priority
            />
          </div>

          {/* Mobile TOC */}
          <div className="mb-8 lg:hidden p-4 bg-secondary/50 rounded-lg">
            <TableOfContents content={post.content} />
          </div>

          {/* Content */}
          <div className="prose-container">
            <MarkdownContent content={post.content} />
          </div>

          {/* Link Previews */}
          <LinkPreviews links={extractLinks(post.content)} />

          {/* Share Buttons */}
          <div className="mt-8">
            <ShareButtons 
              title={post.title}
              url={`${process.env.NEXT_PUBLIC_SITE_URL || 'https://swadesic.com'}/blog/${post.slug}`}
            />
          </div>

          {/* Tags */}
          <div className="mt-8 pt-8 border-t">
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag) => (
                <Link
                  key={tag}
                  href={`/blog/tag/${tag}`}
                  className="text-sm px-3 py-1 bg-secondary hover:bg-secondary/80 rounded-full transition-colors"
                >
                  {tag}
                </Link>
              ))}
            </div>
          </div>

          {/* Related Posts */}
          <RelatedPosts posts={relatedPosts} />
        </div>

        {/* Desktop Sidebar */}
        <div className="hidden lg:block lg:col-span-2 lg:col-start-11">
          <div className="sticky top-8 space-y-8">
            <TableOfContents content={post.content} />
          </div>
        </div>
      </div>
    </article>
  )
}
