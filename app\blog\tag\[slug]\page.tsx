import { blogPosts, tags } from "@/data/blog-data"
import { BlogCard } from "@/components/blog/blog-card"
import { Container } from "@/components/ui/container"
import { notFound } from "next/navigation"
import { Metadata } from "next"

interface TagPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: TagPageProps): Promise<Metadata> {
  const tag = tags.find((t) => t.slug === params.slug)

  if (!tag) {
    return {
      title: "Tag Not Found | Swadesic Blog",
      description: "The requested tag could not be found.",
    }
  }

  return {
    title: `${tag.name} | Swadesic Blog`,
    description: `Explore articles tagged with ${tag.name} on the Swadesic blog.`,
  }
}

export default function TagPage({ params }: TagPageProps) {
  const tag = tags.find((t) => t.slug === params.slug)

  if (!tag) {
    notFound()
  }

  const tagPosts = blogPosts.filter((post) =>
    post.tags.some((t) => t === params.slug)
  )

  return (
    <Container as="main" className="py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">#{tag.name}</h1>
        <p className="text-muted-foreground">
          Showing {tagPosts.length} article{tagPosts.length === 1 ? "" : "s"}
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {tagPosts.map((post) => (
          <BlogCard key={post.id} post={post} />
        ))}
        {tagPosts.length === 0 && (
          <div className="col-span-full text-center py-12">
            <p className="text-muted-foreground">No posts found with this tag.</p>
          </div>
        )}
      </div>
    </Container>
  )
}
