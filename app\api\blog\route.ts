import { NextRequest, NextResponse } from 'next/server'
import { getPublishedBlogPosts, searchPosts } from '@/lib/mdx'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const query = searchParams.get('q') || ''
    const category = searchParams.get('category') || ''
    const author = searchParams.get('author') || ''

    // Get all published posts
    let posts = await getPublishedBlogPosts()

    // Apply search filter
    if (query) {
      posts = searchPosts(posts, query)
    }

    // Apply category filter
    if (category) {
      posts = posts.filter(post =>
        post.categories.some(cat =>
          cat.toLowerCase().replace(/\s+/g, '-') === category.toLowerCase()
        )
      )
    }

    // Apply author filter
    if (author) {
      posts = posts.filter(post =>
        post.author.name.toLowerCase().replace(/\s+/g, '-') === author.toLowerCase()
      )
    }

    // Calculate pagination
    const totalPosts = posts.length
    const totalPages = Math.ceil(totalPosts / limit)
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPosts = posts.slice(startIndex, endIndex)

    return NextResponse.json({
      posts: paginatedPosts,
      pagination: {
        currentPage: page,
        totalPages,
        totalPosts,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    })
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    )
  }
}
