'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Eye, EyeOff } from 'lucide-react';

interface ImageFeatureCardProps {
  title: string;
  description: string;
  image: string;
}

export default function ImageFeatureCard({ title, description, image }: ImageFeatureCardProps) {
  const [showOverlay, setShowOverlay] = useState(true);

  return (
    <div 
      className="group relative bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
      onClick={() => setShowOverlay(!showOverlay)}
    >
      {/* Image Container */}
      <div className="relative w-full">
        <Image
          src={image}
          alt={title}
          width={1920}
          height={1080}
          className="w-full h-auto"
          priority
        />
        {/* Gradient overlay */}
        <div 
          className={`absolute inset-0 bg-gradient-to-t from-black via-black/20 to-transparent transition-opacity duration-300 ${
            showOverlay ? 'opacity-100' : 'opacity-0'
          }`} 
        />
      </div>

      {/* Content overlay */}
      <div 
        className={`absolute bottom-0 left-0 right-0 p-6 text-white transition-all duration-300 ${
          showOverlay ? 'opacity-100' : 'opacity-0'
        }`}
      >
        <h3 className="text-xl font-semibold mb-3">
          {title}
        </h3>
        <p className="text-white/90 text-sm">
          {description}
        </p>
      </div>

      {/* Animated tap indicator */}
      <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-full p-2 transition-all duration-300">
        <div className={`transition-transform duration-300 ${showOverlay ? '' : 'rotate-180'}`}>
          {showOverlay ? (
            <Eye className="w-5 h-5 text-white/90" />
          ) : (
            <EyeOff className="w-5 h-5 text-white/90" />
          )}
        </div>
      </div>
    </div>
  );
}
