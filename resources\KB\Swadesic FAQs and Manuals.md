# 📚 Swadesic FAQ & Manuals Hub

A complete self-serve guide for Stores, Buyers, and Members of the Swadesic ecosystem — built to activate, earn, grow, and thrive with full sovereignty.

---

## 🔰 1. Understanding Swadesic

### Platform Clarity
- What is Swadesic?
- What is Swadesic *not*? (not SaaS, not marketplace, not social media)
- What does “Store Ownership + Discovery + Community” mean in practice?
- Who owns customer data on Swadesic?
- How does Swadesic protect store sovereignty?
- Is Swadesic a private label platform? Can I sell non-Swadeshi products?
- Can I sell the same products elsewhere? Is Swadesic exclusive?

### Mission Alignment
- Why every order supports the mission?
- How to find Swadeshi-first stores?
- How can I contribute more beyond buying?

---

## 🏪 2. Store Setup & Personalization

### Getting Started
- How to get started with Swadesic?
- How to catalog products?
- How to personalize my store?
- How to take photos for my products?

### Product & Store Features
- Swadesic Product page & its features
- Product comment section
- Product versioning
- Difference between default and product-specific settings of stores
- What is My Store Trust Center?

---

## 🔁 3. Order Management

### Order Flows
- How orders are managed?
- Different order flows in Swadesic
- How to process my orders?
- How to build a trustworthy store from Day 1?

### Conflicts & Safety
- What happens if I mess up an order?
- How are order conflicts resolved?
- What support does Swadesic offer stores in conflict scenarios?

---

## ✅ 4. Verification & Trust

- Why is verification important?
- Why enabling orders requires verification?
- How to get verified? What are the types and their benefits?
- How to appeal store verification rejection?
- Why buyers trust verified stores more?

---

## 💸 5. Revenue, Fees & Payouts

### Platform Fees & Payouts
- Full fee structure breakdown with examples (₹50 + GST + PG charges)
- What are examples of orders where Swadesic is *zero fee*?
- Why does Swadesic charge even for offline (pickup) orders?
- How payouts are processed and timeframes
- How payouts are calculated?
- How payouts are initiated?
- How to check store balance?
- How to add shipping balance for Swadesic shipping?
- How does Swadesic shipping margin work?
- How do I see how much Swadesic earned from my store?

### Premium & Upgrades
- When should I upgrade to Premium? What do I unlock?

---

## 📈 6. Growth, Community & Visibility

### Engagement & Community
- What to post in Stores?
- How to tag products in your store post?
- How to use Store messaging group and its features?
- How to build community for my store?
- How to build your first 100 supporters?
- How to create posts that increase product visibility?

### Supporter & Buyer Levels
- What are Flash & Infinity Points? How they help?
- What are Buyer Levels and how do I level up?
- How to use “Supporter Network” to turn buyers into marketers?

---

## 🧠 7. Store Status & Influence

### Progression & Badges
- What is Store Valuation?
- How is Store Valuation calculated?
- What are Store Class Levels? How they help?
- How to level up Store Class?
- What do Founding Store badges unlock?

### Discovery Network Influence
- How to increase store visibility in the discovery network?
- What affects how my store ranks in search/discovery?
- How do influencer commissions work? How does payout happen?
- What are best practices to trigger more Flash/Infinity points?

---

## 🚀 8. Marketing & Referrals

### Promotion & Virality
- How to promote Swadesic Store?
- How to use Instagram page as sales channel for my Swadesic Store?
- How to invite customers to my store?
- How to invite my Instagram audience to Swadesic?
- What do I get when I refer another store to Swadesic?
- How to get featured by Swadesic?
- How to get shoutouts from buyers?
- How to run offline campaigns with QR codes?

---

## 🧩 9. Edge Cases & Exceptions

- I’m banned / blocked — what happened?
- How to handle fraudulent buyers?
- How to handle cash on delivery offline?
- Can I operate multiple stores?
- Can I list services, not products?

---

## 🧭 10. Manuals & Execution Playbooks

### Practical Ops
- How to ask reviews from customers?
- How to migrate from Instagram DM selling to Swadesic?
- How to write product descriptions that convert?
- Best practices for packaging & delivery
- 30-day launch plan for new stores
- How to analyze what’s working in my store?
- What is Store Health Score and how to improve it?
- What are my next 15 moves?

---

## 🧪 11. Advanced: Internal Thinking & Roadmap

> Advanced section for community builders, power users, or future partners.

- What is the Swadesic Feedback Loop? (post → support → buy → share)
- What happens behind the scenes during store verification?
- What is Swadesic's algorithm based on? *(if you plan to disclose)*
- Roadmap: What features are coming next?

---



# 🧠 Swadesic Knowledge Base for Members (Buyers & Supporters)

Welcome to the movement. This KB will help you navigate, engage, and grow within the Swadesic ecosystem — as a supporter, buyer, and community builder.

---

## 🪷 1. Understanding Swadesic (For Citizens, Not Just Consumers)

- What is Swadesic and why was it created?
- How is Swadesic different from Amazon/Flipkart?
- What does “Store Ownership + Discovery + Community” mean for members?
- What is the mission behind Swadesic?
- Why Swadesic is not a marketplace, app, or SaaS — it’s a movement.
- What happens when you support a store?
- How does every order build Bharat’s economic sovereignty?
- Can I buy without registering?
- Who owns the products I buy from?

---

## 🛒 2. Shopping on Swadesic

- How to place an order on Swadesic?
- What are pickup orders vs delivery orders?
- How do I know if a store is verified?
- What are Flash & Infinity Points and how do I earn them?
- How to check order status and tracking?
- What payment methods are supported?
- How do refunds work?
- Is there customer support?

---

## 🧭 3. Becoming a Supporter (More than just a Buyer)

- What does it mean to “Support” a store?
- What happens when I click the Support button?
- What kind of updates do I get from stores I support?
- How do I follow multiple stores?
- How to invite stores I like to join Swadesic?

---

## 🎖️ 4. Supporter Levels & Recognition

- What are Buyer/Supporter Levels?
- What actions give me points? (ordering, commenting, liking, reposting)
- What are the 8 Supporter Levels and what do they unlock?
- How do levels reflect my journey as a Swadesic citizen?
- Can I see my level on my profile?
- How to level up faster?

---

## 🧑‍🤝‍🧑 5. Community Features

- How to comment on a product?
- How to repost a store’s post?
- How to tag products in posts/comments?
- Can I message a store directly?
- How do I find trending Swadeshi products?
- Can I review a store after buying?

---

## 💡 6. Discovery & Mission Shopping

- How to find stores by cause (e.g. women-led, tribal artisans)?
- How to explore by region, language, or category?
- What is the Discover tab and how does it work?
- How to bookmark/favorite products?

---

## 📢 7. Sharing, Referring & Supporting More

- How to share my favorite store with others?
- Can I refer my friends to Swadesic?
- What do I get for referring stores or other buyers?
- How to use QR codes to invite others to Swadesic?
- How to promote my favorite stores online?

---

## 🔐 8. Trust & Safety

- How does Swadesic ensure store legitimacy?
- What is a Verified Store badge?
- What if I have an issue with a store or product?
- What support can I get as a buyer?
- How does Swadesic handle fake products or fraud?

---

## 🧭 9. For Mission-Aligned Citizens

- How do I contribute beyond just buying?
- What is “Economic Sovereignty” and how am I part of it?
- How does my activity help my local economy?
- What is the long-term vision of Swadesic?
- How do I stay updated with new features and missions?

---

## 🧪 10. Advanced (Optional but Inspiring)

- What is the Swadesic Feedback Loop? (support → buy → share → invite)
- How does discovery ranking work for stores I support?
- What’s coming next in the Swadesic ecosystem?

---

## 🙋 11. Troubleshooting & Help

- I didn’t receive my order. What do I do?
- How do I request a refund?
- How do I report a store or product?
- Can I cancel an order after placing?
- How do I change my address or phone number?
