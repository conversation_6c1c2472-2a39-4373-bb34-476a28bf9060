'use client'

import { useState } from 'react'
import { ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import { MarkdownContent } from '@/components/blog/markdown-content'

interface FAQItem {
  question: string
  answer: string
  category?: string
}

interface FAQSectionProps {
  title?: string
  description?: string
  faqs: FAQItem[]
  className?: string
}

export default function FAQSection({ title, description, faqs, className }: FAQSectionProps) {
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  // Group FAQs by category
  const faqsByCategory = faqs.reduce((acc, faq) => {
    const category = faq.category || 'General'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(faq)
    return acc
  }, {} as Record<string, FAQItem[]>)

  return (
    <div className={cn("py-24 sm:py-32", className)}>
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {title && (
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              {title}
            </h1>
            {description && (
              <p className="mt-4 text-lg leading-8 text-muted-foreground">
                {description}
              </p>
            )}
          </div>
        )}
        
        <div className="mx-auto mt-16 max-w-4xl space-y-16">
          {Object.entries(faqsByCategory).map(([category, categoryFaqs]) => (
            <div key={category} className="space-y-8">
              <h2 className="text-2xl font-bold tracking-tight text-gray-900">
                {category}
              </h2>
              <div className="space-y-4">
                {categoryFaqs.map((faq, index) => (
                  <div
                    key={index}
                    className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200"
                  >
                    <button
                      onClick={() => setOpenIndex(openIndex === index ? null : index)}
                      className="flex w-full items-start justify-between p-6 text-left"
                    >
                      <span className="text-lg font-semibold leading-7 text-gray-900">
                        {faq.question.replace(/^\*\*|\*\*$/g, '')}
                      </span>
                      <span className="ml-6 flex h-7 items-center">
                        <motion.div
                          animate={{ rotate: openIndex === index ? 180 : 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <ChevronDown className="h-6 w-6" />
                        </motion.div>
                      </span>
                    </button>
                    <AnimatePresence>
                      {openIndex === index && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6">
                            <MarkdownContent content={faq.answer} />
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
