"use client"

import { useEffect, useState } from "react"

export function ReadingProgress() {
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const updateProgress = () => {
      const scrollTop = window.scrollY
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const readingProgress = scrollTop / docHeight
      setProgress(readingProgress)
    }

    window.addEventListener("scroll", updateProgress)
    updateProgress()

    return () => window.removeEventListener("scroll", updateProgress)
  }, [])

  return (
    <div className="fixed top-0 left-0 w-full h-1 bg-secondary z-50">
      <div
        className="h-full bg-primary transition-all duration-100 ease-out"
        style={{ width: `${progress * 100}%` }}
      />
    </div>
  )
}
