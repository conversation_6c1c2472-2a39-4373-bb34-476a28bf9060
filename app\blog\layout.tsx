import { Metadata } from "next"
import Link from "next/link"
import { Container } from "@/components/ui/container"
import { getAllCategories } from "@/lib/mdx"
import { BlogNavigation } from "@/components/blog/blog-navigation"

export const metadata: Metadata = {
  title: "Blog | Swadesic - Supporting Local Indian Businesses",
  description: "Explore articles about local Indian businesses, artisans, and the Swadeshi movement. Learn how supporting local businesses helps build a stronger economy.",
  openGraph: {
    title: "Swadesic Blog - Supporting Local Indian Businesses",
    description: "Discover stories about local Indian businesses, artisans, and the impact of supporting local communities.",
    type: "website",
    url: "https://swadesic.com/blog",
    siteName: "Swadesic",
  },
}

export default async function BlogLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const categories = await getAllCategories()

  return (
    <div className="min-h-screen">
      {/* Blog Header with Navigation */}
      <div className="border-b sticky top-0 z-10 bg-background">
        <Container>
          <BlogNavigation categories={categories} />
        </Container>
      </div>

      {children}

      {/* Blog CTA */}
      <div className="bg-muted py-12 mt-16">
        <Container>
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Support the Swadeshi Movement</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
              Join our community marketplace for authentic Swadeshi products and help build a stronger local economy.
            </p>
            <div className="flex justify-center">
              <Link
                href="/"
                className="bg-primary text-white px-6 py-3 rounded-md font-medium hover:bg-primary/90 transition-colors"
              >
                Get Started
              </Link>
            </div>
          </div>
        </Container>
      </div>
    </div>
  )
}
