"use client"

// Updated slide indicators with proper containers and responsive layout
import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowUpRight, Pause, Play } from "lucide-react"
import { cn } from "@/lib/utils"

export interface SlideData {
  headline: string
  subheadline: string
  cta: string
  label: string
}

interface HeroSliderProps {
  slides: SlideData[]
  interval?: number // in milliseconds
  className?: string
}

export function HeroSlider({ slides, interval = 5000, className }: HeroSliderProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Function to go to a specific slide
  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  // Function to toggle pause/play
  const togglePause = () => {
    setIsPaused(!isPaused)
  }

  // Set up the timer for auto-sliding
  useEffect(() => {
    // Clear any existing interval first
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }

    // If paused, don't set a new interval
    if (isPaused) {
      return
    }

    // Set new interval
    timerRef.current = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, interval)

    // Cleanup function to clear interval when component unmounts or dependencies change
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }, [interval, slides.length, isPaused])

  return (
    <div className={cn("relative w-full h-[500px] md:h-[600px]", className)}>
      {/* Slides */}
      <div className="relative overflow-hidden h-full min-h-[400px] pb-20 md:pb-0">
        {/* Pause indicator */}
        {isPaused && (
          <div className="absolute top-4 left-4 z-30 bg-black/80 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
            <Pause className="h-3 w-3 mr-1" /> Paused
          </div>
        )}

        {slides.map((slide, index) => (
          <div
            key={index}
            className={cn(
              "transition-opacity duration-700 absolute inset-0 flex items-center justify-center h-full w-full pointer-events-none select-none",
              index === currentSlide ? "opacity-100 z-10 pointer-events-auto select-auto" : "opacity-0 z-0"
            )}
          >
            <div className="mx-auto max-w-[700px] space-y-4 text-center md:pt-16">
              <p className="text-sm text-muted-foreground">
                Swadesic
              </p>
              <h1 className="font-heading text-4xl leading-[1.1] md:text-6xl">
                {slide.headline}
              </h1>
              <p className="text-base text-muted-foreground">
                {slide.subheadline}
              </p>
              <div className="flex flex-col gap-6 mb-16">
                <Button size="lg" className="mx-auto text-white bg-black rounded-full">
                  {slide.cta}
                  <ArrowUpRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}

        {/* Current slide (for initial load before JS) */}
        {/*
        <div className="relative z-0 mx-auto max-w-[700px] space-y-4 text-center md:pt-16">
          <p className="text-sm text-muted-foreground">
            Swadesic
          </p>
          <h1 className="font-heading text-4xl leading-[1.1] md:text-6xl">
            {slides[0].headline}
          </h1>
          <p className="text-base text-muted-foreground">
            {slides[0].subheadline}
          </p>
          <div className="flex flex-col gap-6 mb-16">
            <Button size="lg" className="mx-auto text-white bg-black rounded-full">
              {slides[0].cta}
              <ArrowUpRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
        */}
      </div>

      {/* Desktop Slide Indicators - Vertical */}
      <div className="hidden md:flex absolute right-6 lg:right-12 top-1/2 -translate-y-1/2 flex-col items-end gap-4 z-20 max-w-[200px]">
        <button
          onClick={togglePause}
          className={cn(
            "w-10 h-10 rounded-full flex items-center justify-center shadow-md transition-all duration-300 border",
            isPaused
              ? "bg-black text-white border-transparent"
              : "bg-white/90 backdrop-blur-sm text-black hover:bg-white border-gray-200"
          )}
          aria-label={isPaused ? "Play slides" : "Pause slides"}
          title={isPaused ? "Play slides" : "Pause slides"}
        >
          {isPaused ? (
            <Play className="h-5 w-5" />
          ) : (
            <Pause className="h-5 w-5" />
          )}
        </button>

        {slides.map((slide, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={cn(
              "px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 border text-right min-w-[160px] shadow-sm",
              index === currentSlide
                ? "bg-black text-white shadow-lg border-transparent"
                : "bg-white/95 backdrop-blur-sm text-gray-700 hover:bg-white hover:text-black border-gray-200 hover:shadow-md"
            )}
            aria-label={`Go to slide ${index + 1}`}
            title={`Go to slide ${slide.label}`}
          >
            {slide.label}
          </button>
        ))}
      </div>

      {/* Mobile Slide Indicators - Horizontal */}
      <div className="md:hidden absolute bottom-6 left-0 right-0 flex items-center justify-center z-20 px-4">
        <div className="flex items-center gap-3 max-w-full">
          <button
            onClick={togglePause}
            className={cn(
              "w-10 h-10 rounded-full flex items-center justify-center shadow-md transition-all duration-300 border flex-shrink-0",
              isPaused
                ? "bg-black text-white border-transparent"
                : "bg-white/90 backdrop-blur-sm text-black hover:bg-white border-gray-200"
            )}
            aria-label={isPaused ? "Play slides" : "Pause slides"}
            title={isPaused ? "Play slides" : "Pause slides"}
          >
            {isPaused ? (
              <Play className="h-4 w-4" />
            ) : (
              <Pause className="h-4 w-4" />
            )}
          </button>

          <div className="flex items-center gap-2 overflow-x-auto scrollbar-none max-w-[calc(100vw-120px)]">
            {slides.map((slide, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cn(
                  "px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 border whitespace-nowrap flex-shrink-0",
                  index === currentSlide
                    ? "bg-black text-white shadow-lg border-transparent"
                    : "bg-white/95 backdrop-blur-sm text-gray-700 hover:bg-white hover:text-black border-gray-200"
                )}
                aria-label={`Go to slide ${index + 1}`}
                title={`Go to slide ${slide.label}`}
              >
                {slide.label}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
