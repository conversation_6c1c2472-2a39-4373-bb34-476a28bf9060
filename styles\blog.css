.prose-container {
  max-width: 100%;
  margin: 0 auto;
}

.prose-container > * {
  max-width: 65ch;
  margin-left: auto;
  margin-right: auto;
}

/* Allow certain elements to be full width */
.prose-container > pre,
.prose-container > figure,
.prose-container > div,
.prose-container > table {
  max-width: 100%;
}

/* Remove padding for mobile - handled by Container component */
@media (max-width: 640px) {
  .prose-container > * {
    max-width: 100%;
  }
}
