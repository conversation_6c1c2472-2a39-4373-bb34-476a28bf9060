import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Blog | Swadesic - Supporting Local Indian Businesses",
  description: "Explore articles about local Indian businesses, artisans, and the Swadeshi movement. Learn how supporting local businesses helps build a stronger economy.",
  openGraph: {
    title: "Swadesic Blog - Supporting Local Indian Businesses",
    description: "Discover stories about local Indian businesses, artisans, and the impact of supporting local communities.",
    type: "website",
    url: "https://swadesic.com/blog",
    siteName: "Swadesic",
  },
  twitter: {
    card: "summary_large_image",
    title: "Swadesic Blog - Supporting Local Indian Businesses",
    description: "Discover stories about local Indian businesses, artisans, and the impact of supporting local communities.",
  },
}

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
}
