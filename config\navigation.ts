export type NavItem = {
  title: string
  href?: string
  description?: string
  icon?: string
  external?: boolean
}

export type NavCategory = {
  title: string
  items: NavItem[]
}

export type MainNavItem = {
  title: string
  href?: string
  description?: string
  categories?: NavCategory[]
}

export const mainNav: MainNavItem[] = [
  {
    title: "For Consumers",
    categories: [
      {
        title: "Our Platform",
        items: [
          {
            title: "Being Swadeshi Consumer",
            href: "/",
            description: "Discover Small businesses across Bharat",
          },
          {
            title: "Pricing",
            href: "/consumer-pricing",
            description: "Unlock Revenue sharing with Swadesic Premium",
          },
          {
            title: "Blog",
            href: "/blog",
            description: "Latest insights and updates",
          }
          // {
          //   title: "FAQs",
          //   href: "/consumer-faqs",
          //   description: "Get answers to common questions",
          // }
        ],
      },
      {
        title: "Resources",
        items: [
          // Resource items can be added here
        ]
      }
    ],
  },
  {
    title: "For Business",
    categories: [
      {
        title: "Get Started",
        items: [
          {
            title: "Register Your Business",
            href: "/business",
            description: "Grow & Scale your business online",
          },
          {
            title: "Benefits with Swadesic",
            href: "/business/benefits",
            description: "Discover our powerful features",
          },
        ],
      },
      {
        title: "Know More",
        items: [
          {
            title: "Pricing",
            href: "/business/pricing",
            description: "Simple, transparent pricing",
          },
          {
            title: "Business FAQs",
            href: "/business/faqs",
            description: "Get answers to common questions",
          },
        ],
      },
    ],
  },
  {
    title: "Swadeshi Mission",
    categories: [
      {
        title: "About Us",
        items: [
          {
            title: "Swadeshi Movement 2.0",
            href: "/swadeshi-movement-2",
            description: "Learn about the movement",
          },
          {
            title: "Backstory",
            href: "/backstory",
            description: "Our journey and mission",
          },
        ],
      },

      {
        title: "Company",
        items: [
          {
            title: "Our Parent Company",
            href: "https://sociallyx.com",
            description: "Visit Socially X",
            external: true,
          },
          {
            title: "Team",
            href: "https://sociallyx.com/team",
            description: "Meet the team behind our Products",
            external: true,
          },
          {
            title: "Contact Us",
            href: "/contact",
            description: "Get in touch with us",
          },
        ],
      }
    ],
  },
  {
    title: "Blog",
    href: "/blog",
    description: "Read our articles",
  },
]
