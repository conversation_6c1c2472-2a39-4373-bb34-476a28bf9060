"use client"

import { Search, X } from "lucide-react"
import { useRouter, useSearchParams, usePathname } from "next/navigation"
import { useCallback, useEffect, useState, useRef } from "react"
import { useDebounce } from "@/hooks/use-debounce"

export function SearchBar() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [searchQuery, setSearchQuery] = useState(searchParams.get("q") || "")
  const [isExpanded, setIsExpanded] = useState(false)
  const debouncedSearch = useDebounce(searchQuery, 300)
  const inputRef = useRef<HTMLInputElement>(null)

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString())
      params.set(name, value)
      return params.toString()
    },
    [searchParams]
  )

  useEffect(() => {
    // Only redirect if we're on the blog page or blog category/tag pages
    // Don't redirect if we're on a specific blog post page or author pages
    const isBlogListPage = pathname === '/blog' ||
                          pathname.startsWith('/blog/category/') ||
                          pathname.startsWith('/blog/tag/')

    if (isBlogListPage) {
      if (debouncedSearch) {
        router.push(`/blog?${createQueryString("q", debouncedSearch)}`)
      } else {
        router.push("/blog")
      }
    }
  }, [debouncedSearch, router, createQueryString, pathname])

  // Auto-expand if there's a search query
  useEffect(() => {
    if (searchQuery) {
      setIsExpanded(true)
    }
  }, [searchQuery])

  // Focus input when expanded
  useEffect(() => {
    if (isExpanded && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isExpanded])

  const handleSearchClick = () => {
    setIsExpanded(true)
  }

  const handleClose = () => {
    setIsExpanded(false)
    setSearchQuery("")
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      handleClose()
    }
  }

  if (!isExpanded) {
    return (
      <button
        onClick={handleSearchClick}
        className="flex items-center justify-center p-2 hover:bg-secondary rounded-lg transition-colors"
        aria-label="Search articles"
      >
        <Search className="h-4 w-4 text-muted-foreground" />
      </button>
    )
  }

  return (
    <div className="relative flex items-center">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <input
          ref={inputRef}
          type="search"
          placeholder="Search articles..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          className="w-64 rounded-lg border bg-background px-10 py-2 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
        />
        <button
          onClick={handleClose}
          className="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-secondary rounded-full transition-colors"
          aria-label="Close search"
        >
          <X className="h-3 w-3 text-muted-foreground" />
        </button>
      </div>
    </div>
  )
}
