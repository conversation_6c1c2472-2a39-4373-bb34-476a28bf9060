import { getPublishedBlogPosts } from "@/lib/mdx"
import { InfiniteScroll } from "@/components/blog/infinite-scroll"
import { searchPosts } from "@/lib/search"
import type { BlogPost } from "@/types/blog"
import { Container } from "@/components/ui/container"
import Link from "next/link"
import { BlogCard } from "@/components/blog/blog-card"

export default async function BlogContent({ searchParams }: { 
  searchParams: { [key: string]: string | string[] | undefined } 
}) {
  const query = typeof searchParams.q === "string" ? searchParams.q : ""
  
  let blogPosts: BlogPost[] = [];
  try {
    blogPosts = await getPublishedBlogPosts() || [];
  } catch (error) {
    console.error('Error loading blog posts:', error);
  }
  
  const filteredPosts = query ? searchPosts(blogPosts, query) : blogPosts

  // For search results, show infinite scroll immediately
  if (query) {
    const initialPosts = filteredPosts.slice(0, 12)
    return (
      <Container as="main" className="py-8">
        <div className="space-y-8">
          <div className="space-y-4">
            <h1 className="text-3xl font-bold tracking-tight">Search Results</h1>
            <p className="text-sm text-muted-foreground">
              Found {filteredPosts.length} result{filteredPosts.length === 1 ? "" : "s"} for &quot;{query}&quot;
            </p>
          </div>
          <InfiniteScroll
            initialPosts={initialPosts}
            searchQuery={query}
          />
        </div>
      </Container>
    )
  }

  // Handle case when there are no blog posts
  if (blogPosts.length === 0) {
    return (
      <Container as="main" className="py-8">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">No blog posts found</h1>
          <p className="text-muted-foreground">
            We couldn&apos;t find any blog posts at the moment. Please check back later.
          </p>
        </div>
      </Container>
    )
  }

  // Rest of your blog page content...
  const featuredPost = blogPosts[0];
  const sidePosts = blogPosts.slice(1, 5);
  
  // Group posts by category
  const postsByCategory: Record<string, BlogPost[]> = {}
  
  blogPosts.forEach(post => {
    post.categories?.forEach(category => {
      if (!postsByCategory[category]) {
        postsByCategory[category] = []
      }
      postsByCategory[category].push(post)
    })
  })

  return (
    <Container as="main" className="py-6">
      {/* Hero Section with Featured Post and Latest Posts */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Featured Post - More Compact */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-bold mb-4">Featured Post</h2>
            <div className="bg-card rounded-lg overflow-hidden border border-border">
              <BlogCard post={featuredPost} featured />
            </div>
          </div>

          {/* Latest Posts Sidebar */}
          <div className="lg:col-span-1">
            <h2 className="text-xl font-bold mb-4">Latest Posts</h2>
            <div className="space-y-4">
              {sidePosts.map((post) => (
                <BlogCard key={post.slug} post={post} compact />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* More Posts Grid */}
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">More Articles</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {blogPosts.slice(5, 11).map((post) => (
            <BlogCard key={post.slug} post={post} />
          ))}
        </div>
      </div>

      {/* Categories */}
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">Browse by Category</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Object.entries(postsByCategory).slice(0, 8).map(([category, posts]) => (
            <Link
              key={category}
              href={`/blog/category/${category.toLowerCase().replace(/\s+/g, '-')}`}
              className="group bg-card p-4 rounded-lg border border-border hover:shadow-md transition-all duration-200 hover:-translate-y-1"
            >
              <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">{category}</h3>
              <p className="text-sm text-muted-foreground">{posts.length} article{posts.length === 1 ? '' : 's'}</p>
            </Link>
          ))}
        </div>
      </div>

      {/* Infinite Scroll for More Posts */}
      {blogPosts.length > 11 && (
        <InfiniteScroll
          initialPosts={blogPosts.slice(11)}
        />
      )}
    </Container>
  )
}
