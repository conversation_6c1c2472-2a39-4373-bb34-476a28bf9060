import { getPublishedBlogPosts } from "@/lib/mdx"
import { InfiniteScroll } from "@/components/blog/infinite-scroll"
import { searchPosts } from "@/lib/search"
import type { BlogPost } from "@/types/blog"
import { Container } from "@/components/ui/container"
import Link from "next/link"
import { BlogCard } from "@/components/blog/blog-card"

export default async function BlogContent({ searchParams }: { 
  searchParams: { [key: string]: string | string[] | undefined } 
}) {
  const query = typeof searchParams.q === "string" ? searchParams.q : ""
  
  let blogPosts: BlogPost[] = [];
  try {
    blogPosts = await getPublishedBlogPosts() || [];
  } catch (error) {
    console.error('Error loading blog posts:', error);
  }
  
  const filteredPosts = query ? searchPosts(blogPosts, query) : blogPosts

  // For search results, show infinite scroll immediately
  if (query) {
    const initialPosts = filteredPosts.slice(0, 12)
    return (
      <Container as="main" className="py-8">
        <div className="space-y-8">
          <div className="space-y-4">
            <h1 className="text-3xl font-bold tracking-tight">Search Results</h1>
            <p className="text-sm text-muted-foreground">
              Found {filteredPosts.length} result{filteredPosts.length === 1 ? "" : "s"} for &quot;{query}&quot;
            </p>
          </div>
          <InfiniteScroll
            initialPosts={initialPosts}
            searchQuery={query}
          />
        </div>
      </Container>
    )
  }

  // Handle case when there are no blog posts
  if (blogPosts.length === 0) {
    return (
      <Container as="main" className="py-8">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">No blog posts found</h1>
          <p className="text-muted-foreground">
            We couldn&apos;t find any blog posts at the moment. Please check back later.
          </p>
        </div>
      </Container>
    )
  }

  // Rest of your blog page content...
  const featuredPost = blogPosts[0];
  const sidePosts = blogPosts.slice(1, 5);
  
  // Group posts by category
  const postsByCategory: Record<string, BlogPost[]> = {}
  
  blogPosts.forEach(post => {
    post.categories?.forEach(category => {
      if (!postsByCategory[category]) {
        postsByCategory[category] = []
      }
      postsByCategory[category].push(post)
    })
  })

  return (
    <Container as="main" className="py-8">
      {/* Featured Post */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Featured Post</h2>
        <div className="bg-card rounded-lg overflow-hidden border border-border">
          <BlogCard post={featuredPost} featured />
        </div>
      </div>

      {/* Side Posts */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Latest Posts</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sidePosts.map((post) => (
            <BlogCard key={post.slug} post={post} />
          ))}
        </div>
      </div>

      {/* Categories */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Browse by Category</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(postsByCategory).map(([category, posts]) => (
            <div key={category} className="bg-card p-6 rounded-lg border border-border">
              <h3 className="text-xl font-semibold mb-4">{category}</h3>
              <ul className="space-y-2">
                {posts.slice(0, 4).map((post) => (
                  <li key={post.slug}>
                    <Link 
                      href={`/blog/${post.slug}`}
                      className="text-foreground hover:text-primary transition-colors"
                    >
                      {post.title}
                    </Link>
                  </li>
                ))}
              </ul>
              {posts.length > 4 && (
                <Link 
                  href={`/blog/category/${category.toLowerCase().replace(/\s+/g, '-')}`}
                  className="inline-block mt-4 text-sm text-primary hover:underline"
                >
                  View all {posts.length} posts →
                </Link>
              )}
            </div>
          ))}
        </div>
      </div>
    </Container>
  )
}
