import { getBlogPostBySlug, getAllBlogPosts } from "@/lib/mdx"
import type { BlogPost } from "@/types/blog"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { MarkdownContent } from "@/components/blog/markdown-content"
import { TableOfContents } from "@/components/blog/table-of-contents"
import { RelatedPosts } from "@/components/blog/related-posts"
import { ShareButtons } from "@/components/blog/share-buttons"
import { getRelatedPosts } from "@/lib/related-posts"
import { Container } from "@/components/ui/container"
import { Metadata } from "next"

// Smart TOC Sidebar Component
function SmartTOCSidebar({ content, title, url }: { content: string; title: string; url: string }) {
  return (
    <div className="hidden lg:block fixed right-4 top-24 w-72 xl:w-80 h-[calc(100vh-6rem)] z-10">
      <div
        className="bg-background/95 backdrop-blur-sm border border-border/50 p-6 rounded-xl h-full flex flex-col shadow-xl transition-opacity duration-300"
        id="toc-sidebar"
      >
        <h3 className="font-semibold mb-4 text-sm text-foreground">On this page</h3>
        <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-secondary scrollbar-track-transparent">
          <TableOfContents content={content} />
        </div>

        {/* Share Buttons */}
        <div className="mt-4 pt-4 border-t border-border/50">
          <h3 className="font-semibold mb-3 text-sm text-foreground">Share</h3>
          <ShareButtons title={title} url={url} />
        </div>
      </div>

      <script
        dangerouslySetInnerHTML={{
          __html: `
            (function() {
              let isScrolling = false;
              let scrollTimeout;
              
              function handleTOCVisibility() {
                if (isScrolling) return;
                isScrolling = true;
                
                const sidebar = document.getElementById('toc-sidebar');
                const contentEnd = document.querySelector('[data-content-end]');
                const footer = document.querySelector('footer');

                if (!sidebar || !contentEnd) {
                  isScrolling = false;
                  return;
                }


                const contentEndRect = contentEnd.getBoundingClientRect();
                const viewportHeight = window.innerHeight;
                const isNearBottom = contentEndRect.top < viewportHeight * 0.8;
                
                // Only hide TOC when user scrolls near the very bottom of the content
                if (isNearBottom) {
                  sidebar.style.opacity = '0';
                  sidebar.style.pointerEvents = 'none';
                } else {
                  sidebar.style.opacity = '1';
                  sidebar.style.pointerEvents = 'auto';
                }
                
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                  isScrolling = false;
                }, 100);
              }


              // Use requestAnimationFrame for smoother performance
              let ticking = false;
              
              function onScroll() {
                if (!ticking) {
                  window.requestAnimationFrame(() => {
                    handleTOCVisibility();
                    ticking = false;
                  });
                  ticking = true;
                }
              }


              // Add scroll and resize listeners
              window.addEventListener('scroll', onScroll, { passive: true });
              window.addEventListener('resize', onScroll, { passive: true });

              // Initial check with a small delay to ensure DOM is ready
              setTimeout(handleTOCVisibility, 300);
              
              // Additional check after content loads
              if (document.readyState === 'complete') {
                handleTOCVisibility();
              } else {
                window.addEventListener('load', handleTOCVisibility);
              }
            })();
          `
        }}
      />
    </div>
  )
}

// CTA Component
function BlogCTASection() {
  return (
    <section className="mt-16">
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8 lg:p-12">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/10 to-pink-600/10" />
        <div className="absolute top-0 left-0 w-full h-full opacity-30">
          <div className="absolute top-4 left-4 w-2 h-2 bg-red-500 rounded-full" />
          <div className="absolute top-8 right-8 w-1 h-1 bg-blue-500 rounded-full" />
          <div className="absolute bottom-6 left-8 w-1.5 h-1.5 bg-purple-500 rounded-full" />
        </div>

        <div className="relative z-10 max-w-2xl">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4 leading-tight">
            Build What Can’t Be Taken Away- {" "}
            <span className="bg-gradient-to-r from-[#FF9933] via-white to-[#138808] bg-clip-text text-transparent">
              Ownership
            </span>
          </h2>
          <p className="text-gray-300 text-lg mb-8 leading-relaxed">
            Create your store for free, grow sales and community.
          </p>
          <Link
            href="/sell"
            className="inline-flex items-center px-8 py-3 bg-white text-gray-900 font-semibold rounded-full hover:bg-gray-100 transition-all duration-200 hover:scale-105 shadow-lg"
          >
            Start Owning
          </Link>
        </div>
      </div>
    </section>
  )
}

interface BlogPostPageProps {
  params: {
    slug: string
  }
  searchParams: {
    preview?: string
  }
}

export async function generateMetadata({ params, searchParams }: BlogPostPageProps): Promise<Metadata> {
  const isPreview = searchParams.preview === 'true'
  const post = await getBlogPostBySlug(params.slug, isPreview)

  if (!post) {
    return {
      title: "Post Not Found | Swadesic Blog",
      description: "The requested blog post could not be found.",
    }
  }

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://swadesic.com'
  const postUrl = `${siteUrl}/blog/${post.slug}`

  // For draft posts, exclude from SEO
  const baseMetadata = {
    title: `${post.title} | Swadesic Blog`,
    description: post.excerpt,
  }

  if (post.status === 'draft') {
    return {
      ...baseMetadata,
      robots: {
        index: false,
        follow: false,
      },
    }
  }

  return {
    ...baseMetadata,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: "article",
      publishedTime: post.date,
      authors: [post.author.name],
      images: [
        {
          url: post.coverImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
      url: postUrl,
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: [post.coverImage],
    },
  }
}

export default async function BlogPostPage({ params, searchParams }: BlogPostPageProps) {
  console.log('[BlogPostPage] Component mounted')
  console.log('[BlogPostPage] Received params:', params)
  console.log('[BlogPostPage] Received searchParams:', searchParams)
  
  const isPreview = searchParams.preview === 'true'
  console.log(`[BlogPostPage] Looking for post with slug: ${params.slug}, isPreview: ${isPreview}`)
  
  let post: BlogPost;
  let relatedPosts: BlogPost[] = [];
  
  try {
    console.log('[BlogPostPage] Calling getBlogPostBySlug...')
    const postResult = await getBlogPostBySlug(params.slug, isPreview)
    
    if (!postResult) {
      console.error(`[BlogPostPage] Post not found: ${params.slug}`)
      notFound()
    }
    
    post = postResult;
    console.log('[BlogPostPage] Found post:', {
      slug: post.slug,
      title: post.title,
      status: post.status,
      hasContent: !!post.content
    })
    
    console.log('[BlogPostPage] Fetching all blog posts...')
    const allPosts = await getAllBlogPosts()
    console.log(`[BlogPostPage] Found ${allPosts.length} total posts`)
    
    relatedPosts = getRelatedPosts(post, allPosts)
    console.log(`[BlogPostPage] Found ${relatedPosts.length} related posts`)
    
  } catch (error) {
    console.error('[BlogPostPage] Error loading blog post:', error)
    throw error // Re-throw to let Next.js handle the error
  }

  console.log('[BlogPostPage] Rendering component')
  
  return (
    <div 
      className="relative min-h-screen bg-gradient-to-br from-background to-secondary/20"
      data-page-loaded={true}
    >
      {/* Draft Banner */}
      {post.status === 'draft' && (
        <div className="bg-yellow-100 border-b border-yellow-200 px-4 py-2">
          <div className="max-w-7xl mx-auto">
            <p className="text-sm text-yellow-800 font-medium">
              🚧 This is a draft post and is not visible to the public.
            </p>
          </div>
        </div>
      )}

      {/* Main Content Area with proper sidebar spacing */}
      <div className="lg:mr-80 xl:mr-96">
        <Container as="article" className="py-8 lg:py-12">
          {/* Header */}
          <header className="mb-8">
            {/* Categories Links */}
            <div className="mb-4">
              <div className="flex flex-wrap items-center gap-x-2 gap-y-1">
                {post.categories.map((category, index) => (
                  <div key={category} className="flex items-center">
                    <Link
                      href={`/blog/category/${category.toLowerCase()}`}
                      className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors"
                    >
                      {category}
                    </Link>
                    {index < post.categories.length - 1 && (
                      <span className="text-gray-400 mx-2">•</span>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Main Title */}
            <h1 className="text-4xl lg:text-5xl font-bold mb-6 leading-tight text-gray-900">
              {post.title}
            </h1>

            {/* Author and Date Info */}
            <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-gray-600 text-sm mb-8">
              <div className="flex items-center gap-x-2">
                <span>By</span>
                <div className="flex items-center gap-x-2">
                  <Link 
                    href={`/blog/author/${post.author.name.toLowerCase().replace(/\s+/g, '-')}`} 
                    className="flex items-center gap-x-2 hover:text-gray-900 transition-colors"
                  >
                    <span className="w-6 h-6 rounded-full overflow-hidden flex-shrink-0 inline-block">
                      <Image
                        src={post.author.image}
                        alt={post.author.name}
                        width={24}
                        height={24}
                        className="w-full h-full object-cover"
                      />
                    </span>
                    <span className="font-medium text-gray-900">
                      {post.author.name}
                    </span>
                  </Link>
                </div>
              </div>
              <span className="text-gray-400">•</span>
              <time dateTime={post.date} className="font-medium">
                {new Date(post.date).toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                  year: "numeric",
                })}
              </time>
            </div>
          </header>

          {/* Cover Image - Moved here to be after header but before content */}
          <div className="mb-8">
            <div className="relative aspect-[2/1] overflow-hidden rounded-xl shadow-lg">
              <Image
                src={post.coverImage}
                alt={post.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>

          {/* Content */}
          <div className="prose-container max-w-none mx-auto">
            <div>
              <MarkdownContent content={post.content} />
            </div>
          </div>

          {/* Content End Marker - Used for TOC visibility detection */}
          <div data-content-end className="h-1"></div>

          {/* Tags */}
          <div className="mt-12 pt-8 border-t border-border/50">
            <h3 className="text-sm font-medium text-muted-foreground mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
                {post.tags.map((tag: string) => (
                <Link
                  key={tag}
                  href={`/blog/tag/${tag}`}
                  className="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-secondary/80 hover:bg-secondary rounded-full transition-all duration-200 hover:scale-105"
                >
                  #{tag}
                </Link>
              ))}
            </div>
          </div>

          {/* Related Posts */}
          <div className="mt-12">
            <RelatedPosts posts={relatedPosts} />
          </div>

          {/* CTA Section */}
          <BlogCTASection />

          {/* Mobile FAB for Table of Contents */}
          <div className="lg:hidden">
            <TableOfContents content={post.content} isMobile />
          </div>
        </Container>
      </div>

      {/* Smart TOC Sidebar - Hides when past content */}
      <SmartTOCSidebar
        content={post.content}
        title={post.title}
        url={`${process.env.NEXT_PUBLIC_SITE_URL || 'https://swadesic.com'}/blog/${post.slug}`}
      />
    </div>
  )
}
