import { Container } from "@/components/ui/container";
import fs from "fs";
import path from "path";
import { Metadata } from "next";
import { MarkdownContent } from "@/components/blog/markdown-content";

export const metadata: Metadata = {
  title: "Seller Terms and Conditions | Swadesic",
  description: "Terms and Conditions for Swadesic sellers and business partners",
};

export default async function SellerTermsAndConditions() {
  // Read the markdown file
  const markdownContent = fs.readFileSync(
    path.join(process.cwd(), "public/Markdowns/policies/terms-and-conditions-seller.md"),
    "utf-8"
  );

  return (
    <Container as="main" className="py-12 sm:py-16">
      <article className="prose prose-lg dark:prose-invert mx-auto px-4 md:px-8">
        <MarkdownContent content={markdownContent} />
      </article>
    </Container>
  );
}
