"use client";

import React from "react";
import ReactMarkdown from "react-markdown";
import { Prism as Synta<PERSON><PERSON><PERSON>light<PERSON>, SyntaxHighlighterP<PERSON> } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/cjs/styles/prism";
import Image from "next/image";
import { ExternalLink } from "lucide-react";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import remarkGfm from "remark-gfm";

interface MarkdownContentProps {
  content: string;
}

export function MarkdownContent({ content }: MarkdownContentProps) {
  // Process content to remove duplicates and unwanted text
  let processedContent = content
    .replace(/^##?\s*.*?\n/, '') // Remove first heading
    .replace(/!\[.*?\]\(.*?1436129.*?\)\n?/, '') // Remove cover image
    .trim(); // Remove extra whitespace

  // Remove specific unwanted text patterns (case insensitive and flexible)
  const unwantedPatterns = [
    /^We know,?\s*we know\.?\s*We'?re spoiling you\.?\s*$/gim,
    /^But hey,?\s*you gave feedback,?\s*we listened,?\s*and now we have four updates.*$/gim,
    /^This week:?\s*enhanced marketplace features.*$/gim,
    /^Keep reading for the details\.?\s*$/gim,
    /^But hey.*$/gim,
    /^This week:.*$/gim,
  ];

  unwantedPatterns.forEach(pattern => {
    processedContent = processedContent.replace(pattern, '');
  });

  // Clean up extra whitespace and empty lines
  processedContent = processedContent
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Replace multiple empty lines with double newline
    .trim();

  return (
    <ReactMarkdown
      rehypePlugins={[rehypeRaw, rehypeSanitize]}
      remarkPlugins={[remarkGfm]}
      components={{
        h1: ({ children }) => {
          const text = String(children)
          const id = text.toLowerCase()
            .replace(/[^\w\s\-]/g, '') // Remove special chars except hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
            .replace(/-+/g, '-') // Collapse multiple hyphens
          return (
            <h2 id={id} className="text-2xl font-bold mb-4 mt-8 text-foreground scroll-mt-24">
              {children}
            </h2>
          )
        },
        h2: ({ children }) => {
          const text = String(children)
          const id = text.toLowerCase()
            .replace(/[^\w\s\-]/g, '') // Remove special chars except hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
            .replace(/-+/g, '-') // Collapse multiple hyphens
          return (
            <h2 id={id} className="text-2xl font-bold mb-4 mt-8 text-foreground scroll-mt-24">
              {children}
            </h2>
          )
        },
        h3: ({ children }) => {
          const text = String(children)
          const id = text.toLowerCase()
            .replace(/[^\w\s\-]/g, '') // Remove special chars except hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
            .replace(/-+/g, '-') // Collapse multiple hyphens
          return (
            <h3 id={id} className="text-xl font-semibold mb-3 mt-6 text-foreground scroll-mt-24">
              {children}
            </h3>
          )
        },
        h4: ({ children }) => {
          const text = String(children)
          const id = text.toLowerCase()
            .replace(/[^\w\s\-]/g, '') // Remove special chars except hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
            .replace(/-+/g, '-') // Collapse multiple hyphens
          return (
            <h4 id={id} className="text-lg font-semibold mb-2 mt-4 text-foreground scroll-mt-24">
              {children}
            </h4>
          )
        },
        h5: ({ children }) => {
          const text = String(children)
          const id = text.toLowerCase()
            .replace(/[^\w\s\-]/g, '') // Remove special chars except hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
            .replace(/-+/g, '-') // Collapse multiple hyphens
          return (
            <h5 id={id} className="text-base font-semibold mb-2 mt-4 text-foreground scroll-mt-24">
              {children}
            </h5>
          )
        },
        h6: ({ children }) => {
          const text = String(children)
          const id = text.toLowerCase()
            .replace(/[^\w\s\-]/g, '') // Remove special chars except hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
            .replace(/-+/g, '-') // Collapse multiple hyphens
          return (
            <h6 id={id} className="text-sm font-semibold mb-2 mt-4 text-foreground scroll-mt-24">
              {children}
            </h6>
          )
        },
        p: ({ children }) => (
          <p className="mb-6 leading-relaxed">{children}</p>
        ),
        ul: ({ children }) => (
          <ul className="list-disc list-inside mb-6 space-y-2">{children}</ul>
        ),
        ol: ({ children }) => (
          <ol className="list-decimal list-inside mb-6 space-y-2">{children}</ol>
        ),
        li: ({ children }) => <li className="text-base">{children}</li>,
        a: ({ href, children }) => {
          if (!href) return <>{children}</>;

          const isExternal = href.startsWith("http");
          return (
            <a
              href={href}
              target={isExternal ? "_blank" : undefined}
              rel={isExternal ? "noopener noreferrer" : undefined}
              className="text-black hover:text-primary underline decoration-primary/30 hover:decoration-primary transition-colors"
            >
              {children}
              {isExternal && (
                <ExternalLink className="inline-block w-3 h-3 ml-1 mb-0.5" />
              )}
            </a>
          );
        },
        blockquote: ({ children }) => (
          <blockquote className="border-l-4 border-primary pl-4 italic mb-6">
            {children}
          </blockquote>
        ),
        code: ({
          children,
          inline = false,
          className,
          ...props
        }: React.ComponentProps<'code'> & { inline?: boolean }) => {
          const match = /language-(\w+)/.exec(className || "");
          const language = match ? match[1] : "text";

          return !inline ? (
            <div className="my-6 rounded-lg overflow-hidden">
              <SyntaxHighlighter
                language={language}
                style={oneDark as SyntaxHighlighterProps['style']}
                PreTag="div"
                customStyle={{
                  margin: 0,
                  padding: "1rem",
                  fontSize: "0.875rem",
                }}
              >
                {String(children).replace(/\n$/, "")}
              </SyntaxHighlighter>
            </div>
          ) : (
            <code
              className="bg-secondary px-1.5 py-0.5 rounded text-sm"
              {...props}
            >
              {children}
            </code>
          );
        },
        img: ({ src, alt }) => (
          <figure className="my-8">
            <div className="relative aspect-[16/9] w-full overflow-hidden rounded-lg">
              <Image
                src={src || ""}
                alt={alt || ""}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw"
              />
            </div>
            {alt && (
              <figcaption className="text-sm text-center mt-2 text-muted-foreground">
                {alt}
              </figcaption>
            )}
          </figure>
        ),
        hr: () => <hr className="my-8 border-border" />,
        table: ({ children }) => (
          <div className="my-6 w-full overflow-x-auto">
            <table className="min-w-full divide-y divide-border border border-border">
              {children}
            </table>
          </div>
        ),
        th: ({ children }) => (
          <th className="px-6 py-3 bg-muted text-left text-sm font-semibold text-black border-b border-border">
            {children}
          </th>
        ),
        td: ({ children }) => (
          <td className="px-6 py-4 whitespace-nowrap text-sm text-black border-b border-border">
            {children}
          </td>
        ),
      }}
    >
      {processedContent}
    </ReactMarkdown>
  );
}
