import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import readingTime from 'reading-time'
import { BlogPost, BlogCategory, BlogTag } from '@/types/blog'

const contentDirectory = path.join(process.cwd(), 'content')

export interface BlogPostFrontmatter {
  title: string
  slug: string
  excerpt: string
  coverImage: string
  date: string
  author: string
  categories: string[]
  tags: string[]
  readingTime?: string
  status?: 'draft' | 'published'
  seo?: {
    metaTitle?: string
    metaDescription?: string
    keywords?: string[]
  }
}

export interface AuthorData {
  name: string
  image: string
  bio?: string
  social?: {
    twitter?: string
    linkedin?: string
    github?: string
  }
}

// Get all blog posts (including drafts for admin)
export async function getAllBlogPosts(includeDrafts = false): Promise<BlogPost[]> {
  console.log('[getAllBlogPosts] Starting to load blog posts')
  const blogDir = path.join(contentDirectory, 'blog')

  if (!fs.existsSync(blogDir)) {
    console.error(`[getAllBlogPosts] Blog directory not found: ${blogDir}`)
    return []
  }

  const posts: BlogPost[] = []

  // Recursively read all MDX files
  function readPostsFromDir(dir: string) {
    console.log(`[getAllBlogPosts] Reading directory: ${dir}`)
    const items = fs.readdirSync(dir)
    console.log(`[getAllBlogPosts] Found items:`, items)

    for (const item of items) {
      const itemPath = path.join(dir, item)
      const stat = fs.statSync(itemPath)

      if (stat.isDirectory()) {
        readPostsFromDir(itemPath)
      } else if (item.endsWith('.mdx')) {
        console.log(`[getAllBlogPosts] Processing file: ${itemPath}`)
        try {
          const fileContents = fs.readFileSync(itemPath, 'utf8')
          const { data, content } = matter(fileContents)
          const frontmatter = data as BlogPostFrontmatter

          console.log(`[getAllBlogPosts] Processed post: ${frontmatter.slug}`)
          console.log(`[getAllBlogPosts] Post date: ${frontmatter.date}`)
          console.log(`[getAllBlogPosts] Post status: ${frontmatter.status || 'published'}`)

          // Calculate reading time
          const readingTimeResult = readingTime(content)

          // Get author data
          const authorData = getAuthorData(frontmatter.author)

          const post: BlogPost = {
            id: frontmatter.slug,
            title: frontmatter.title,
            slug: frontmatter.slug,
            excerpt: frontmatter.excerpt,
            content,
            coverImage: frontmatter.coverImage,
            date: frontmatter.date,
            author: {
              name: authorData.name,
              image: authorData.image,
            },
            categories: frontmatter.categories,
            tags: frontmatter.tags,
            readingTime: frontmatter.readingTime || readingTimeResult.text,
            status: frontmatter.status || 'published',
          }


          // Filter drafts unless includeDrafts is true
          if (includeDrafts || post.status === 'published') {
            console.log(`[getAllBlogPosts] Adding post to results: ${post.slug}`)
            posts.push(post)
          } else {
            console.log(`[getAllBlogPosts] Skipping draft post: ${post.slug}`)
          }
        } catch (error) {
          console.error(`[getAllBlogPosts] Error processing file ${itemPath}:`, error)
        }
      }
    }
  }

  readPostsFromDir(blogDir)

  // Sort posts by date (newest first)
  return posts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
}

// Get published blog posts only
export async function getPublishedBlogPosts(): Promise<BlogPost[]> {
  return getAllBlogPosts(false)
}

// Get a single blog post by slug
export async function getBlogPostBySlug(slug: string, includeDrafts = false): Promise<BlogPost | null> {
  console.log(`[getBlogPostBySlug] Looking for slug: ${slug}`)
  const posts = await getAllBlogPosts(includeDrafts)
  console.log(`[getBlogPostBySlug] Found ${posts.length} posts`)
  
  const foundPost = posts.find(post => {
    console.log(`[getBlogPostBySlug] Checking post: ${post.slug} (${post.title})`)
    return post.slug === slug
  })
  
  console.log(`[getBlogPostBySlug] Found post:`, foundPost ? foundPost.slug : 'Not found')
  return foundPost || null
}

// Get author data
export function getAuthorData(authorSlug: string): AuthorData {
  const authorPath = path.join(contentDirectory, 'authors', `${authorSlug}.json`)

  if (fs.existsSync(authorPath)) {
    const authorData = JSON.parse(fs.readFileSync(authorPath, 'utf8'))
    return authorData
  }

  // Fallback author data
  return {
    name: authorSlug.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    image: '/images/authors/default.jpg',
  }
}

// Get all categories
export async function getAllCategories(): Promise<BlogCategory[]> {
  const posts = await getAllBlogPosts()
  const categoryMap = new Map<string, number>()

  posts.forEach(post => {
    post.categories.forEach(category => {
      categoryMap.set(category, (categoryMap.get(category) || 0) + 1)
    })
  })

  return Array.from(categoryMap.entries()).map(([name, count]) => ({
    name,
    slug: name.toLowerCase().replace(/\s+/g, '-'),
    count,
  }))
}

// Get all tags
export async function getAllTags(): Promise<BlogTag[]> {
  const posts = await getAllBlogPosts()
  const tagMap = new Map<string, number>()

  posts.forEach(post => {
    post.tags.forEach(tag => {
      tagMap.set(tag, (tagMap.get(tag) || 0) + 1)
    })
  })

  return Array.from(tagMap.entries()).map(([name, count]) => ({
    name,
    slug: name.toLowerCase().replace(/\s+/g, '-'),
    count,
  }))
}

// Get posts by category slug
export async function getPostsByCategory(categorySlug: string): Promise<BlogPost[]> {
  const posts = await getPublishedBlogPosts()

  // Simple category matching by slug and name conversion
  const categoryName = categorySlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())

  return posts.filter(post =>
    post.categories?.some(cat => {
      const catSlug = cat.toLowerCase().replace(/\s+/g, '-')
      return catSlug === categorySlug || cat.toLowerCase() === categoryName.toLowerCase()
    })
  )
}

// Get posts by tag
export async function getPostsByTag(tagSlug: string): Promise<BlogPost[]> {
  const posts = await getPublishedBlogPosts()

  return posts.filter(post =>
    post.tags.some(tag =>
      tag.toLowerCase().replace(/\s+/g, '-') === tagSlug
    )
  )
}

// Get posts by author
export async function getPostsByAuthor(authorSlug: string): Promise<BlogPost[]> {
  const posts = await getPublishedBlogPosts()

  return posts.filter(post =>
    post.author.name.toLowerCase().replace(/\s+/g, '-') === authorSlug
  )
}

// Search posts
export function searchPosts(posts: BlogPost[], query: string): BlogPost[] {
  const lowercaseQuery = query.toLowerCase()

  return posts.filter(post =>
    post.title.toLowerCase().includes(lowercaseQuery) ||
    post.excerpt.toLowerCase().includes(lowercaseQuery) ||
    post.content.toLowerCase().includes(lowercaseQuery) ||
    post.categories.some(cat => cat.toLowerCase().includes(lowercaseQuery)) ||
    post.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  )
}
