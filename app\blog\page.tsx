import { Suspense } from 'react'
import Loading from './loading'

export const dynamic = 'force-dynamic'

interface BlogPageProps {
  searchParams: { [key: string]: string | string[] | undefined }
}

export default function BlogPage({ searchParams }: BlogPageProps) {
  return (
    <Suspense fallback={<Loading />}>
      <BlogContent searchParams={searchParams} />
    </Suspense>
  )
}

async function BlogContent({
  searchParams
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const { default: dynamicContent } = await import('./blog-content')
  return dynamicContent({ searchParams })
}
