import { Suspense } from 'react'
import Loading from './loading'

export const dynamic = 'force-dynamic'

interface BlogPageProps {
  searchParams: { [key: string]: string | string[] | undefined }
}

export default function BlogPage({ searchParams }: BlogPageProps) {
  return (
    <Suspense fallback={<Loading />}>
      <BlogContent searchParams={searchParams} />
    </Suspense>
  )
}

async function BlogContent({ 
  searchParams 
}: { 
  searchParams: { [key: string]: string | string[] | undefined } 
}) {
  const { default: dynamicContent } = await import('./blog-content')
  return dynamicContent({ searchParams })
  const query = typeof searchParams.q === "string" ? searchParams.q : ""
  
  let blogPosts: BlogPost[] = [];
  try {
    blogPosts = await getPublishedBlogPosts() || [];
  } catch (error) {
    console.error('Error loading blog posts:', error);
    // Continue with empty array to prevent build failure
  }
  
  const filteredPosts = query ? searchPosts(blogPosts, query) : blogPosts

  // For search results, show infinite scroll immediately
  if (query) {
    // Get first page of search results (12 posts)
    const initialPosts = filteredPosts.slice(0, 12)

    return (
      <Container as="main" className="py-8">
        <div className="space-y-8">
          <div className="space-y-4">
            <h1 className="text-3xl font-bold tracking-tight">Search Results</h1>
            <p className="text-sm text-muted-foreground">
              Found {filteredPosts.length} result{filteredPosts.length === 1 ? "" : "s"} for &quot;{query}&quot;
            </p>
          </div>

          <InfiniteScroll
            initialPosts={initialPosts}
            searchQuery={query}
          />
        </div>
      </Container>
    )
  }

  // Handle case when there are no blog posts
  if (blogPosts.length === 0) {
    return (
      <Container as="main" className="py-8">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">No blog posts found</h1>
          <p className="text-muted-foreground">
            We couldn&apos;t find any blog posts at the moment. Please check back later.
          </p>
        </div>
      </Container>
    );
  }

  // Featured post is the most recent one
  const featuredPost = blogPosts[0];
  // Side posts are the next 4 posts
  const sidePosts = blogPosts.slice(1, 5);

  // Group posts by category
  const postsByCategory: Record<string, BlogPost[]> = {}

  blogPosts.forEach(post => {
    post.categories.forEach(category => {
      if (!postsByCategory[category]) {
        postsByCategory[category] = []
      }
      postsByCategory[category].push(post)
    })
  })

  return (
    <Container as="main" className="py-8">
      {!query && (
        <>
          {/* Hero Section - Featured Post + Side Posts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {/* Featured Post */}
            <div>
              <Link href={`/blog/${featuredPost.slug}`} className="group block">
                <div className="relative aspect-video w-full overflow-hidden rounded-lg mb-4">
                  <Image
                    src={featuredPost.coverImage}
                    alt={featuredPost.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                    priority
                  />
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    {featuredPost.categories.slice(0, 1).map(category => (
                      <Link
                        key={category}
                        href={`/blog/category/${category.toLowerCase()}`}
                        className="text-sm px-3 py-1 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
                      >
                        {category}
                      </Link>
                    ))}
                  </div>
                  <h2 className="text-2xl font-bold group-hover:text-primary transition-colors line-clamp-2">
                    {featuredPost.title}
                  </h2>
                  <p className="text-muted-foreground line-clamp-2">
                    {featuredPost.excerpt}
                  </p>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>by {featuredPost.author.name}</span>
                    <span>•</span>
                    <time dateTime={featuredPost.date}>
                      {new Date(featuredPost.date).toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                        year: "numeric",
                      })}
                    </time>
                  </div>
                </div>
              </Link>
            </div>

            {/* Side Posts */}
            <div className="space-y-6">
              {sidePosts.map(post => (
                <div key={post.id} className="flex gap-4">
                  <Link href={`/blog/${post.slug}`} className="group block flex-shrink-0">
                    <div className="relative w-24 h-16 overflow-hidden rounded-lg">
                      <Image
                        src={post.coverImage}
                        alt={post.title}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                  </Link>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      {post.categories.slice(0, 1).map(category => (
                        <Link
                          key={category}
                          href={`/blog/category/${category.toLowerCase()}`}
                          className="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
                        >
                          {category}
                        </Link>
                      ))}
                    </div>
                    <h3 className="font-semibold text-sm line-clamp-2 mb-1">
                      <Link
                        href={`/blog/${post.slug}`}
                        className="hover:text-primary transition-colors"
                      >
                        {post.title}
                      </Link>
                    </h3>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>by {post.author.name}</span>
                      <span>•</span>
                      <time dateTime={post.date}>
                        {new Date(post.date).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                          year: "numeric",
                        })}
                      </time>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Category Sections */}
          {Object.entries(postsByCategory).slice(0, 3).map(([category, posts]) => (
            <div key={category} className="mb-16">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold">{category}</h2>
                <Link
                  href={`/blog/category/${category.toLowerCase()}`}
                  className="text-sm font-medium flex items-center gap-1 text-blue-600 hover:text-blue-700 transition-colors"
                >
                  View all →
                </Link>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {posts.slice(0, 4).map(post => (
                  <BlogCard key={post.id} post={post} />
                ))}
              </div>
            </div>
          ))}
        </>
      )}


    </Container>
  )
}
