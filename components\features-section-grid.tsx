"use client"

import Image from "next/image"
import { ArrowRight } from "lucide-react"

interface Feature {
  title: string
  description: string
  image: string
  benefits: string[]
}

interface FeaturesSectionGridProps {
  className?: string
  features: Feature[]
  title: string
  description: string
}

export default function FeaturesSectionGrid({
  className,
  features,
  title,
  description
}: FeaturesSectionGridProps) {
  return (
    <section className={`w-full py-12 md:py-24 bg-gray-50 ${className}`}>
      <div className="container px-4 md:px-6">
        <div className="text-center max-w-[800px] mx-auto mb-12">
          <h2 className="text-3xl font-semibold tracking-tight sm:text-4xl mb-4">
            {title}
          </h2>
          <p className="text-base text-muted-foreground">
            {description}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
          {features.map((feature, index) => (
            <div key={index} className="bg-white rounded-xl p-6 shadow-sm">
              <div className="aspect-video mb-6 rounded-lg overflow-hidden">
                <Image
                  src={feature.image}
                  alt={feature.title}
                  width={600}
                  height={400}
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
              <p className="text-muted-foreground mb-4">{feature.description}</p>
              <ul className="space-y-2">
                {feature.benefits.map((benefit, idx) => (
                  <li key={idx} className="flex items-center gap-2">
                    <ArrowRight className="h-4 w-4 text-primary shrink-0" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
