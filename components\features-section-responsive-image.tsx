"use client"

import Image from "next/image"
import { Card } from "@/components/ui/card"

interface ResponsiveFeature {
  title: string
  description: string
  mobileImage: string
  desktopImage: string
}

interface FeaturesSectionResponsiveImageProps {
  className?: string
  feature: ResponsiveFeature
}

export default function FeaturesSectionResponsiveImage({
  className,
  feature
}: FeaturesSectionResponsiveImageProps) {
  return (
    <section className={`w-full py-12 md:py-24 ${className}`}>
      <div className="container px-4 md:px-6">
        <Card className="overflow-hidden border-none shadow-none bg-transparent">
          <div className="max-w-[800px] mx-auto text-center mb-8">
            <h2 className="text-3xl tracking-tight md:text-5xl mb-4">
              {feature.title}
            </h2>
            <p className="text-base text-muted-foreground">
              {feature.description}
            </p>
          </div>
          
          {/* Mobile Image (Hidden on desktop) */}
          <div className="md:hidden">
            <div className="overflow-hidden rounded-2xl bg-gray-100">
              <div className="max-h-[80vh] overflow-y-auto rounded-2xl">
                <Image
                  src={feature.mobileImage}
                  alt={feature.title}
                  width={400}
                  height={1200}
                  className="w-full h-auto"
                  quality={100}
                />
              </div>
            </div>
          </div>

          {/* Desktop Image (Hidden on mobile) */}
          <div className="hidden md:block">
            <div className="overflow-hidden rounded-2xl bg-gray-100">
              <div className="relative h-[700px] rounded-2xl">
                <Image
                  src={feature.desktopImage}
                  alt={feature.title}
                  fill
                  className="object-contain"
                  quality={100}
                />
              </div>
            </div>
          </div>
        </Card>
      </div>
    </section>
  )
}
