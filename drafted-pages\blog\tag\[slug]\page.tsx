import { blogPosts, categories, tags } from "@/data/blog-data"
import { BlogCard } from "@/components/blog/blog-card"
import { BlogSidebar } from "@/components/blog/blog-sidebar"
import { notFound } from "next/navigation"

interface TagPageProps {
  params: {
    slug: string
  }
}

export default function TagPage({ params }: TagPageProps) {
  const tag = tags.find((t) => t.slug === params.slug)
  const filteredPosts = blogPosts.filter((post) =>
    post.tags.some((t) => t === params.slug)
  )

  if (!tag) {
    notFound()
  }

  return (
    <div className="container py-12">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="md:col-span-2">
          <h1 className="text-3xl font-bold mb-8">Tag: {tag.name}</h1>
          <div className="grid grid-cols-1 gap-8">
            {filteredPosts.map((post) => (
              <BlogCard key={post.id} post={post} />
            ))}
          </div>
        </div>

        {/* Sidebar */}
        <div className="md:col-span-1">
          <BlogSidebar categories={categories} tags={tags} />
        </div>
      </div>
    </div>
  )
}
