# Project Overview
Build me a next js 14 website for my technology company Swadesic. 
Swadesic is an Community marketplace for Swadeshi products. The website should appeal to Consumers and Businesses. 

Make the website SEO friendly, responsive and UI UX should be minimal and simple. Use strictly shadcn components. Use lucide react icons



# Core functionalities/pages
## Home page - landing page for Customers (General Users) (/)
## Bussiness page - landing page for Businesses (Business Owners) (/business)
## Pricing page - (/pricing)

# Doc


# Important implementation notes
Use npx shadcn@latest add instead of npx shadcn-ui@latest add, it is deprecated

# Routes
Fix Header to contain. 


Home - 
- Being Swadeshi Consumer (/)
- Pricing (/consumer-pricing) 
- Affiliate Program (/affiliate-program)
- FAQs (/consumer-faqs)


For Business - 
- Register your business (/business)
- Pricing (/business/pricing)
- Benefits with Swadesic (/business/benefits)
- Business FAQs (/business/faqs)

Swadeshi Mission
- Swadeshi Movement 2.0 (/swadeshi-movement-2)
- Backstory (/backstory)
- Company (redirect to Socially X at https://sociallyx.com)
- Team (redirect to Socially X at https://sociallyx.com/team)
- Contact us (/contact-us)

Blog 
- Blog (/blog)
