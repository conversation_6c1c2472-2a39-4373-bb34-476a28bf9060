import { BlogPost } from "@/types/blog"

export function searchPosts(posts: BlogPost[], query: string): BlogPost[] {
  const searchTerms = query.toLowerCase().split(" ")

  return posts.filter((post) => {
    const searchableText = `
      ${post.title} 
      ${post.excerpt} 
      ${post.content} 
      ${post.categories.join(" ")} 
      ${post.tags.join(" ")}
    `.toLowerCase()

    return searchTerms.every((term) => searchableText.includes(term))
  })
}

export function highlightText(text: string, query: string): string {
  if (!query) return text

  const searchTerms = query.toLowerCase().split(" ")
  let highlightedText = text

  searchTerms.forEach((term) => {
    const regex = new RegExp(`(${term})`, "gi")
    highlightedText = highlightedText.replace(regex, "<mark>$1</mark>")
  })

  return highlightedText
}
