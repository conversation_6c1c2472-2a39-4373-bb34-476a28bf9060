"use client"

import Link from "next/link"
import { SearchBar } from "./search-bar"

interface Category {
  name: string
  slug: string
  count: number
}

interface BlogNavigationProps {
  categories: Category[]
}

export function BlogNavigation({ categories }: BlogNavigationProps) {
  return (
    <div className="flex items-center overflow-x-auto py-4 gap-6 whitespace-nowrap">
      {/* Search Bar as first item */}
      <div className="flex-shrink-0">
        <SearchBar />
      </div>
      
      <Link href="/blog" className="text-sm font-medium hover:text-primary transition-colors">
        Recent
      </Link>
      
      {categories.map(category => (
        <Link
          key={category.slug}
          href={`/blog/category/${category.slug}`}
          className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
        >
          {category.name}
        </Link>
      ))}
      
      <Link href="/blog/authors" className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
        Authors
      </Link>
    </div>
  )
}
