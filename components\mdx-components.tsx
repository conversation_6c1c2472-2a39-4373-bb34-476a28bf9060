import type { MDXComponents } from 'mdx/types'
import Image from 'next/image'
import Link from 'next/link'
import { ExternalLink } from 'lucide-react'

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    // Headings
    h1: ({ children }) => (
      <h1 className="text-3xl font-bold mb-6 mt-8 text-foreground scroll-mt-24">
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-2xl font-bold mb-4 mt-8 text-foreground scroll-mt-24">
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-xl font-semibold mb-3 mt-6 text-foreground scroll-mt-24">
        {children}
      </h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-lg font-semibold mb-2 mt-4 text-foreground scroll-mt-24">
        {children}
      </h4>
    ),
    
    // Paragraphs and text
    p: ({ children }) => (
      <p className="mb-6 leading-relaxed text-foreground">{children}</p>
    ),
    
    // Lists
    ul: ({ children }) => (
      <ul className="list-disc list-inside mb-6 space-y-2 text-foreground">{children}</ul>
    ),
    ol: ({ children }) => (
      <ol className="list-decimal list-inside mb-6 space-y-2 text-foreground">{children}</ol>
    ),
    li: ({ children }) => (
      <li className="text-base leading-relaxed">{children}</li>
    ),
    
    // Links
    a: ({ href, children }) => {
      if (!href) return <>{children}</>
      
      const isExternal = href.startsWith('http')
      
      if (isExternal) {
        return (
          <a
            href={href}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:text-primary/80 underline decoration-primary/30 hover:decoration-primary transition-colors inline-flex items-center gap-1"
          >
            {children}
            <ExternalLink className="w-3 h-3" />
          </a>
        )
      }
      
      return (
        <Link
          href={href}
          className="text-primary hover:text-primary/80 underline decoration-primary/30 hover:decoration-primary transition-colors"
        >
          {children}
        </Link>
      )
    },
    
    // Images
    img: ({ src, alt }) => (
      <figure className="my-8">
        <div className="relative aspect-[16/9] w-full overflow-hidden rounded-lg">
          <Image
            src={src || ''}
            alt={alt || ''}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
          />
        </div>
        {alt && (
          <figcaption className="text-sm text-center mt-2 text-muted-foreground">
            {alt}
          </figcaption>
        )}
      </figure>
    ),
    
    // Blockquotes
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-primary pl-4 italic mb-6 text-muted-foreground">
        {children}
      </blockquote>
    ),
    
    // Code
    code: ({ children, className }) => {
      const isInline = !className
      
      if (isInline) {
        return (
          <code className="bg-secondary px-1.5 py-0.5 rounded text-sm font-mono">
            {children}
          </code>
        )
      }
      
      return (
        <div className="my-6 rounded-lg overflow-hidden">
          <pre className="bg-secondary p-4 overflow-x-auto">
            <code className="text-sm font-mono">{children}</code>
          </pre>
        </div>
      )
    },
    
    // Tables
    table: ({ children }) => (
      <div className="my-6 w-full overflow-x-auto">
        <table className="min-w-full divide-y divide-border border border-border">
          {children}
        </table>
      </div>
    ),
    th: ({ children }) => (
      <th className="px-6 py-3 bg-muted text-left text-sm font-semibold text-foreground border-b border-border">
        {children}
      </th>
    ),
    td: ({ children }) => (
      <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground border-b border-border">
        {children}
      </td>
    ),
    
    // Horizontal rule
    hr: () => <hr className="my-8 border-border" />,
    
    // Strong and emphasis
    strong: ({ children }) => (
      <strong className="font-semibold text-foreground">{children}</strong>
    ),
    em: ({ children }) => (
      <em className="italic text-foreground">{children}</em>
    ),
    
    ...components,
  }
}
