"use client"

import Image from "next/image"
import { ArrowRight } from "lucide-react"

interface Feature {
  title: string
  description: string
  image: string
  benefits: string[]
}

interface FeaturesSectionCenteredProps {
  className?: string
  feature: Feature
  reversed?: boolean
}

export default function FeaturesSectionCentered({
  className,
  feature,
  reversed = false
}: FeaturesSectionCenteredProps) {
  return (
    <section className={`w-full py-12 md:py-24 ${className}`}>
      <div className="container px-4 md:px-6">
        <div className={`flex flex-col items-center gap-8 text-center ${reversed ? 'md:flex-col-reverse' : ''}`}>
          <div className="max-w-[600px] space-y-4">
            <h2 className="text-3xl font-semibold tracking-tight sm:text-4xl">
              {feature.title}
            </h2>
            <p className="text-base text-muted-foreground">
              {feature.description}
            </p>
            <ul className="space-y-2 text-left">
              {feature.benefits.map((benefit, index) => (
                <li key={index} className="flex items-center gap-2">
                  <ArrowRight className="h-4 w-4 text-primary shrink-0" />
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
          <div className="w-full max-w-[800px]">
            <div className="rounded-lg overflow-hidden">
              <Image
                src={feature.image}
                alt={feature.title}
                width={800}
                height={450}
                className="w-full h-auto object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
