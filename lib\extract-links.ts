export function extractLinks(content: string): string[] {
  // Match markdown and regular URLs
  const markdownLinkRegex = /\[([^\]]+)\]\(([^)]+)\)/g
  const urlRegex = /(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/g

  const links = new Set<string>()

  // Extract markdown links
  let match
  while ((match = markdownLinkRegex.exec(content)) !== null) {
    const url = match[2]
    if (url.startsWith('http')) {
      links.add(url)
    }
  }

  // Extract plain URLs
  while ((match = urlRegex.exec(content)) !== null) {
    links.add(match[1])
  }

  return Array.from(links)
}
