import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Phone, Mail, MessageSquare, Instagram, Twitter, Youtube, MessageCircle, ArrowUpRight } from "lucide-react"

export default function ContactPage() {
  return (
    <div className="flex flex-col min-h-screen overflow-x-hidden">
      <main className="flex-1 overflow-x-hidden">
        {/* Hero Section */}
        <div className="flex min-h-[75vh] items-start sm:items-center justify-center bg-white px-4 pt-32 sm:p-4 md:py-8">
          <div className="mx-auto max-w-[700px] space-y-4 text-center">
            <p className="text-sm text-muted-foreground">
              Contact Us
            </p>
            <h1 className="font-heading text-4xl leading-[1.1] md:text-6xl">
              We&apos;re Here to Help You
            </h1>
            <p className="text-base text-muted-foreground">
              Choose your preferred way to connect with us
            </p>
            <div className="max-w-xl mx-auto my-24">
              <div className="flex flex-col p-8 rounded-2xl bg-black text-white shadow-lg">
                <div className="flex items-center gap-3 mb-4">
                  <MessageCircle className="h-6 w-6" />
                  <h2 className="text-xl font-medium">Chat on WhatsApp</h2>
                </div>
                <p className="text-gray-300 mb-6">
                  Get instant support on WhatsApp - Our fastest response channel
                </p>
                <Button asChild size="lg" className="mt-auto bg-white text-black hover:bg-white/90 rounded-full border-0">
                  <Link 
                    href="https://wa.me/918331086680" 
                    target="_blank"
                    rel="noopener noreferrer"
                    className="gap-2"
                  >
                    Start WhatsApp Chat
                    <ArrowUpRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Methods Section */}
        <section className="w-full py-12 md:py-24 bg-gray-50">
          <div className="container px-4 md:px-6">
            {/* Priority WhatsApp Card */}
            <div className="text-center max-w-[800px] mx-auto mb-12">
              <h2 className="text-3xl font-medium tracking-tight md:text-5xl mb-4">
                Alternatively
              </h2>
              <p className="text-base text-muted-foreground">
                Reach out to us through any of the following options
              </p>
            </div>
            
          

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {/* Phone Support */}
              <div className="overflow-hidden rounded-2xl border bg-white shadow-sm">
                <div className="p-8">
                  <div className="flex items-center gap-3 mb-4">
                    <Phone className="h-6 w-6 text-[var(--brand-color)]" />
                    <h3 className="text-xl font-medium">Phone Support</h3>
                  </div>
                  <p className="text-muted-foreground mb-6">
                    Available Everyday, from 10 AM to 6 PM
                  </p>
                  <Button asChild size="lg" variant="outline" className="w-full rounded-full">
                    <Link href="tel:+918331086680" className="gap-2">
                      +91 83310 86680
                      <ArrowUpRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>

              {/* Email Support */}
              <div className="overflow-hidden rounded-2xl border bg-white shadow-sm">
                <div className="p-8">
                  <div className="flex items-center gap-3 mb-4">
                    <Mail className="h-6 w-6 text-[var(--brand-color)]" />
                    <h3 className="text-xl font-medium">Email Support</h3>
                  </div>
                  <p className="text-muted-foreground mb-6">
                    We typically respond within 24 hours
                  </p>
                  <Button asChild size="lg" variant="outline" className="w-full rounded-full">
                    <Link href="mailto:<EMAIL>" className="gap-2">
                      <EMAIL>
                      <ArrowUpRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>

              {/* In-App Support */}
              <div className="overflow-hidden rounded-2xl border bg-white shadow-sm">
                <div className="p-8">
                  <div className="flex items-center gap-3 mb-4">
                    <MessageSquare className="h-6 w-6 text-[var(--brand-color)]" />
                    <h3 className="text-xl font-medium">In-App Support</h3>
                  </div>
                  <p className="text-muted-foreground mb-6">
                    Get quick help within the Swadesic app
                  </p>
                  <Button asChild size="lg" variant="outline" className="w-full rounded-full">
                    <Link 
                      href="https://play.google.com/store/apps/details?id=com.sociallyx.swadesic&hl=en_SG" 
                      className="gap-2"
                    >
                      Open Support
                      <ArrowUpRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>

            {/* Social Links */}
            <div className="text-center max-w-[800px] mx-auto mt-24">
              <h2 className="text-3xl font-medium tracking-tight md:text-5xl mb-4">
                Connect With Us
              </h2>
              <p className="text-base text-muted-foreground mb-12">
                Follow us on social media for updates and announcements
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button asChild size="lg" variant="outline" className="rounded-full">
                  <Link 
                    href="https://instagram.com/swadesic" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    aria-label="Follow us on Instagram"
                    className="gap-2"
                  >
                    <Instagram className="h-5 w-5" />
                    Follow on Instagram
                  </Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="rounded-full">
                  <Link 
                    href="https://x.com/SwadesicApp" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    aria-label="Follow us on X (Twitter)"
                    className="gap-2"
                  >
                    <Twitter className="h-5 w-5" />
                    Follow on Twitter
                  </Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="rounded-full">
                  <Link 
                    href="https://www.youtube.com/@swadesic" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    aria-label="Subscribe to our YouTube channel"
                    className="gap-2"
                  >
                    <Youtube className="h-5 w-5" />
                    Subscribe on YouTube
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}
