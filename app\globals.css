@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --background: #ffffff;
    --foreground: #000000;
    --card: #ffffff;
    --card-foreground: #020817;
    --popover: #ffffff;
    --popover-foreground: #020817;
    --primary: #10c056;
    --primary-foreground: #f8fafc;
    --secondary: #f1f5f9;
    --secondary-foreground: #1e293b;
    --muted: #f1f5f9;
    --muted-foreground: #64748b;
    --accent: #f1f5f9;
    --accent-foreground: #1e293b;
    --destructive: #ef4444;
    --destructive-foreground: #f8fafc;
    --border: #e2e8f0;
    --input: #e2e8f0;
    --ring: #10c056;
    --radius: 0.5rem;
    --brand-color: #10c056;
  }
/* 
  .dark {
    --background: #0c0a09;
    --foreground: #f2f2f2;
    --card: #1c1917;
    --card-foreground: #f2f2f2;
    --popover: #171717;
    --popover-foreground: #f2f2f2;
    --primary: #e11d48;
    --primary-foreground: #fff1f2;
    --secondary: #27272a;
    --secondary-foreground: #fafafa;
    --muted: #262626;
    --muted-foreground: #a3a3a3;
    --accent: #292524;
    --accent-foreground: #fafafa;
    --destructive: #7f1d1d;
    --destructive-foreground: #fef2f2;
    --border: #27272a;
    --input: #27272a;
    --ring: #e11d48;
    --brand-color: #10c056;
  } */
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  /* Hide scrollbar for carousel */
  .scrollbar-none {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }

  /* Navigation Menu Positioning */
  .relative {
    position: relative;
  }

  .navigation-menu-content {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  /* Navigation Menu Styles */
  .navigation-menu-content {
    animation: none !important;
    transition: opacity 150ms ease-out;
  }

  .navigation-menu-content[data-state="open"] {
    opacity: 1;
    pointer-events: auto;
    visibility: visible;
  }

  .navigation-menu-content[data-state="closed"] {
    opacity: 0;
    pointer-events: none;
    visibility: hidden;
  }

  /* Navigation Menu Trigger Styles */
  .navigation-menu-trigger {
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 8px 12px !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  .navigation-menu-trigger:hover {
    background: transparent !important;
  }

  .navigation-menu-trigger[data-state="open"] {
    background: transparent !important;
    color: hsl(var(--foreground)) !important;
  }

  /* Remove default radix animations */
  .navigation-menu-content[data-motion^="from-"],
  .navigation-menu-content[data-motion^="to-"] {
    animation: none !important;
  }

  /* Navigation Link Hover Effects */
  .group:hover .group-hover\:text-primary {
    color: var(--primary);
    transition: color 150ms ease;
  }

  /* Fix dropdown positioning */
  .navigation-menu {
    position: relative;
    z-index: 40;
  }

  .navigation-menu-content > div {
    margin-top: 0;
    border-top: 1px solid var(--border);
  }
}
