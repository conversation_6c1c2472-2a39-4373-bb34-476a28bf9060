@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --background: #ffffff;
    --foreground: #000000;
    --card: #ffffff;
    --card-foreground: #020817;
    --popover: #ffffff;
    --popover-foreground: #020817;
    --primary: #10c056;
    --primary-foreground: #f8fafc;
    --secondary: #f1f5f9;
    --secondary-foreground: #1e293b;
    --muted: #f1f5f9;
    --muted-foreground: #64748b;
    --accent: #f1f5f9;
    --accent-foreground: #1e293b;
    --destructive: #ef4444;
    --destructive-foreground: #f8fafc;
    --border: #e2e8f0;
    --input: #e2e8f0;
    --ring: #10c056;
    --radius: 0.5rem;
    --brand-color: #10c056;
  }

  .dark {
    --background: #0a0a0a; /* Very dark grey/almost black */
    --foreground: #e0e0e0; /* Light grey for text */
    --card: #1a1a1a; /* Slightly lighter dark for cards */
    --card-foreground: #e0e0e0; /* Light grey for card text */
    --popover: #1a1a1a; /* Same as card for popovers */
    --popover-foreground: #e0e0e0; /* Light grey for popover text */
    --primary: #0e9f4e; /* A slightly darker, but still vibrant green */
    --primary-foreground: #f0f0f0; /* Off-white for primary text */
    --secondary: #2a2a2a; /* Darker grey for secondary elements */
    --secondary-foreground: #d0d0d0; /* Lighter grey for secondary text */
    --muted: #2a2a2a; /* Same as secondary for muted */
    --muted-foreground: #9ca3af; /* Medium grey for muted text */
    --accent: #2a2a2a; /* Same as secondary for accent */
    --accent-foreground: #d0d0d0; /* Lighter grey for accent text */
    --destructive: #dc2626; /* A slightly darker red for destructive actions */
    --destructive-foreground: #f0f0f0; /* Off-white for destructive text */
    --border: #333333; /* Darker grey for borders */
    --input: #333333; /* Same as border for input fields */
    --ring: #0e9f4e; /* Same as primary for ring focus */
    --brand-color: #10c056; /* Brand color typically remains consistent or has a specific dark variant if needed */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  /* Hide scrollbar for carousel */
  .scrollbar-none {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }

  /* Navigation Menu Positioning */
  .relative {
    position: relative;
  }

  .navigation-menu-content {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  /* Navigation Menu Styles */
  .navigation-menu-content {
    animation: none !important;
    transition: opacity 150ms ease-out;
  }

  .navigation-menu-content[data-state="open"] {
    opacity: 1;
    pointer-events: auto;
    visibility: visible;
  }

  .navigation-menu-content[data-state="closed"] {
    opacity: 0;
    pointer-events: none;
    visibility: hidden;
  }

  /* Navigation Menu Trigger Styles */
  .navigation-menu-trigger {
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 8px 12px !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  .navigation-menu-trigger:hover {
    background: transparent !important;
  }

  .navigation-menu-trigger[data-state="open"] {
    background: transparent !important;
    color: hsl(var(--foreground)) !important;
  }

  /* Remove default radix animations */
  .navigation-menu-content[data-motion^="from-"],
  .navigation-menu-content[data-motion^="to-"] {
    animation: none !important;
  }

  /* Navigation Link Hover Effects */
  .group:hover .group-hover\:text-primary {
    color: var(--primary);
    transition: color 150ms ease;
  }

  /* Fix dropdown positioning */
  .navigation-menu {
    position: relative;
    z-index: 40;
  }

  .navigation-menu-content > div {
    margin-top: 0;
    border-top: 1px solid var(--border);
  }
}
